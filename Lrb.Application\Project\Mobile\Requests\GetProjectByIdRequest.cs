﻿namespace Lrb.Application.Project.Mobile
{
    public class GetProjectByIdRequest : IRequest<Response<ViewProjectDto>>
    {
        public Guid Id { get; set; }
        public GetProjectByIdRequest(Guid id)
        {
            Id = id;
        }
    }
    public class GetProjectByIdRequestHandler : IRequestHandler<GetProjectByIdRequest, Response<ViewProjectDto>>
    {
        public readonly IRepositoryWithEvents<Domain.Entities.Project> _projectRepo;
        private readonly IRepositoryWithEvents<CustomMasterAmenity> _projectAmenitiesRepo;

        public GetProjectByIdRequestHandler(IRepositoryWithEvents<Domain.Entities.Project> leadRepo, IRepositoryWithEvents<CustomMasterAmenity> projectAmenitiesRepo)
        {
            _projectRepo = leadRepo;
            _projectAmenitiesRepo = projectAmenitiesRepo;
        }

        public async Task<Response<ViewProjectDto>> Handle(GetProjectByIdRequest request, CancellationToken cancellationToken)
        {
            var project = (await _projectRepo.ListAsync(new ProjectByIdSpec(request.Id), cancellationToken)).FirstOrDefault();
            if (project == null) { throw new NotFoundException("No Project found by this Id"); }
            var projectDto = project.Adapt<ViewProjectDto>();
            if (project.Amenities?.Any() ?? false)
            {
                var amenitiesIds = project.Amenities.Select(i => i.MasterProjectAmenityId).ToList();
                var amenities = await _projectAmenitiesRepo.ListAsync(new GetCustomAmenitiesByIdsSpec(amenitiesIds), cancellationToken);
                projectDto.ProjectAmenities = amenities.Select(i => i.Id).ToList();
            }
            return new Response<ViewProjectDto>(projectDto);
        }
    }

}
