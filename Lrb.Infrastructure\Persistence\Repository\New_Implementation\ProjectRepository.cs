﻿using Lrb.Application.Common.Persistence;
using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.Project.Web.Requests;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Enums;
using Lrb.Infrastructure.Persistence.Context;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Serilog;

namespace Lrb.Infrastructure.Persistence.Repository.New_Implementation
{
    public class ProjectRepository : EFRepository<Lrb.Domain.Entities.Project>, IProjectRepository
    {
        private readonly IServiceProvider _provider;
        private readonly ILogger _logger;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        public ProjectRepository(ApplicationDbContext dbContext, IServiceProvider provider, Serilog.ILogger logger, ILeadRepositoryAsync leadRepositoryAsync) : base(dbContext)
        {
            _provider = provider;
            _logger = logger;
            _leadRepositoryAsync = leadRepositoryAsync;
        }

        //public async Task<bool> AddAsync(TempProjects project)
        //{
        //    var scope = _provider.CreateScope();
        //    var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        //    try
        //    {
        //        await context.TempProjects.AddAsync(project);
        //        await context.SaveChangesAsync();
        //    }
        //    catch (Exception ex)
        //    {
        //        var error = new LrbError()
        //        {
        //            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
        //            ErrorSource = ex?.Source,
        //            StackTrace = ex?.StackTrace,
        //            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
        //            ErrorModule = "ProjectRepository -> AddAsync()"
        //        };
        //        await _leadRepositoryAsync.AddErrorAsync(error);
        //    }
        //    return true;
        //}

        public async Task<bool> AddAsync(Project project)
        {
            var scope = _provider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            try
            {
                await context.Projects.AddAsync(project);
                await context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "ProjectRepository -> AddAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            return true;
        }

        public async Task<IEnumerable<TempProjects>> GetProjectsByNameAsync(List<string> projects)
        {
            var query = BuildQuery(projects);
            return await query.ToListAsync();
        }
        public async Task<TempProjects?> GetProjectByNameAsync(string project)
        {
            var query = BuildQuery(project);
            return await query.FirstOrDefaultAsync();
        }
        private IQueryable<TempProjects> BuildQuery(List<string> projects)
        {
            var scope = _provider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            var query = context.TempProjects.Where(i => !i.IsDeleted && projects.Contains(i.Name.ToLower().Trim()))
               .AsQueryable();
            return query;
        }
        private IQueryable<TempProjects> BuildQuery(string project)
        {
            var scope = _provider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            var query = context.TempProjects.Where(i => !i.IsDeleted && i.Name.ToLower().Trim() == project)
               .AsQueryable();
            return query;
        }
        public async Task<IEnumerable<Lrb.Domain.Entities.Project>> GetAllProjectsForWebNewAsync(Lrb.Application.Project.Web.Requests.GetAllProjectRequest request, List<Guid> projectIds)
        {
            var query = BuildQueryForWeb(request, projectIds);
            query = query.Skip(request.PageSize * (request.PageNumber - 1))
                .Take(request.PageSize);
            try
            {
                var projects = await query.ToListAsync();
                return projects;
            }
            catch (Exception e)
            {
                throw;
            }
        }
        public async Task<int> GetAllProjectsCountForWebNewAsync(Lrb.Application.Project.Web.Requests.GetAllProjectRequest request, List<Guid> projectIds)
        {
            var query = BuildQueryForWeb(request, projectIds);
            try
            {
                return await query.CountAsync();

            }
            catch (Exception e)
            {
                throw;
            }
        }

        public async Task<IEnumerable<Lrb.Domain.Entities.Project>> GetAllProjectsForMobileNewAsync(Lrb.Application.Project.Mobile.GetAllProjectRequest request, List<Guid> projectIds)
        {
            var query = BuildQueryForMobile(request, projectIds);
            query = query.Skip(request.PageSize * (request.PageNumber - 1))
                .Take(request.PageSize);
            try
            {
                var projects = await query.ToListAsync();
                return projects;
            }
            catch (Exception e)
            {
                throw;
            }
        }
        public async Task<int> GetAllProjectsCountForMobileNewAsync(Lrb.Application.Project.Mobile.GetAllProjectRequest request, List<Guid> projectIds)
        {
            var query = BuildQueryForMobile(request, projectIds);
            try
            {
                return await query.CountAsync();

            }
            catch (Exception e)
            {
                throw;
            }
        }

        private IQueryable<Lrb.Domain.Entities.Project> BuildQueryForWeb(Application.Project.Web.Requests.GetAllProjectRequest request, List<Guid>? projectIds)
        {
            var scope = _provider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

            if ((request.MinLeadCount != null || request.MaxLeadCount != null ||
                 request.MinProspectCount != null || request.MaxProspectCount != null)
                && (projectIds?.Any() != true))
            {
                return context.Projects.Where(_ => false);
            }
            IQueryable<Lrb.Domain.Entities.Project>? query = null;
            query = context.Projects
                .Where(i => !i.IsDeleted)
                .Include(i => i.Amenities)
                .Include(i => i.ProjectGalleries)
                .Include(i => i.MonetaryInfo)
                .Include(i => i.ProjectType)
                .Include(i => i.Address)
                    .ThenInclude(i => i.Location)
                        .ThenInclude(i => i.Zone)
                            .ThenInclude(i => i.City)
                .Include(i => i.BuilderDetail)
                .Include(i => i.UserAssignment)
                .Include(i => i.AssociatedBanks)
                .OrderBy(i => i.CurrentStatus)
                .ThenByDescending(i => i.LastModifiedOn - new DateTime(2000, 01, 01, 00, 00, 00, DateTimeKind.Utc)).AsQueryable();
            switch (request.ProjectVisibility)
            {
                case ProjectVisibilityType.All:
                    query = query.Where(i => !i.IsArchived);
                    break;

                case ProjectVisibilityType.Residential:
                    query = query.Where(i => !i.IsArchived).Where(j => (j.ProjectType.Id == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c")) ||
                                                                       (j.ProjectType.BaseId == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c")));
                    break;

                case ProjectVisibilityType.Commercial:
                    query = query.Where(i => !i.IsArchived).Where(j => (j.ProjectType.Id == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573")) ||
                                                                       (j.ProjectType.BaseId == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573")));
                    break;

                case ProjectVisibilityType.Agriculture:
                    query = query.Where(i => !i.IsArchived).Where(j => (j.ProjectType.Id == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8")) ||
                                                                       (j.ProjectType.BaseId == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8")));
                    break;

                case ProjectVisibilityType.Deleted:
                    query = query.Where(i => i.IsArchived);
                    break;
            }

            if (!string.IsNullOrWhiteSpace(request.Search))
            {
                request.Search = request.Search.ToLower().Trim();

                query = query.Where(i => (i.Name.ToLower().Trim().Contains(request.Search)) || (i.Certificates.ToLower().Trim().Contains(request.Search)) ||
                    (i.Address.SubLocality.ToLower().Trim().Contains(request.Search)) ||
                    (i.Address.City.ToLower().Trim().Contains(request.Search)) || (i.Address.Locality.ToLower().Trim().Contains(request.Search)) ||
                    (i.Address.State.ToLower().Trim().Contains(request.Search)) || (i.Address.Country.ToLower().Trim().Contains(request.Search)) ||
                    (i.Address.PostalCode.ToLower().Trim().Contains(request.Search)) || (i.ProjectType.DisplayName.ToLower().Trim().Contains(request.Search)));
            }

            if (request.ProjectStatuses?.Any() ?? false)
            {
                query = query.Where(i => request.ProjectStatuses.Contains(i.Status));
            }

            if (request.CurrentStatus != null)
            {
                query = query.Where(i => i.CurrentStatus == request.CurrentStatus);
            }

            if (request.ProjectType != null)
            {
                switch (request.ProjectType)
                {
                    case Lrb.Application.Project.Web.Requests.ProjectType.All:
                        break;

                    case Lrb.Application.Project.Web.Requests.ProjectType.Residential:
                        query = query.Where(j => (j.ProjectType.Id == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c")) ||
                                                                       (j.ProjectType.BaseId == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c")));
                        break;

                    case Lrb.Application.Project.Web.Requests.ProjectType.Commercial:
                        query = query.Where(j => (j.ProjectType.Id == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573")) ||
                                                                       (j.ProjectType.BaseId == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573")));
                        break;

                    case Lrb.Application.Project.Web.Requests.ProjectType.Agricultural:
                        query = query.Where(j => (j.ProjectType.Id == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8")) ||
                                                                       (j.ProjectType.BaseId == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8")));
                        break;
                }
            }

            if (request.ProjectSubType?.Any() ?? false)
            {
                query = query.Where(i => request.ProjectSubType.Contains(i.ProjectType.Id));
            }

            if (request.MinPrice != null || request.MaxPrice != null)
            {
                if (request.MinPrice != null && request.MaxPrice != null)
                {
                    query = query.Where(i => i.MinimumPrice >= request.MinPrice && i.MaximumPrice <= request.MaxPrice);
                }
                else if (request.MinPrice != null && request.MaxPrice == null)
                {
                    query = query.Where(i => i.MinimumPrice >= request.MinPrice);
                }
                else if (request.MinPrice == null && request.MaxPrice != null)
                {
                    query = query.Where(i => i.MaximumPrice <= request.MaxPrice);
                }
            }

            if ((request.FromDate != null && request.FromDate != default) || (request.ToDate != null && request.ToDate != default))
            {
                DateTime? tempToDate = request?.ToDate?.ConvertToDateToUtc();
                DateTime? tempFromDate = request?.FromDate?.ConvertFromDateToUtc();

                if ((request?.FromDate != null && request.FromDate != default) && (request.ToDate != null && request.ToDate != default))
                {
                    query = query.Where(i => i.StartDate >= tempFromDate && i.EndDate <= tempToDate);
                }
                else if ((request?.FromDate != null && request.FromDate != default) && (request.ToDate == null && request.ToDate == default))
                {
                    query = query.Where(i => i.StartDate >= tempFromDate);
                }
                else if ((request?.FromDate == null && request?.FromDate == default) && (request?.ToDate != null && request.ToDate != default))
                {
                    query = query.Where(i => i.EndDate <= tempToDate);
                }
            }
            if (request?.Locations != null && request.Locations.Any())
            {
                request.Locations = request.Locations.ConvertAll(i => i.Replace(",", "").ToLower().Trim().Replace(" ", "")).ToList();
                query = query.Where(i => request.Locations.Contains((i.Address.SubLocality + "" +
                   i.Address.Locality + "" +
                   i.Address.Community + "" +
                   i.Address.SubCommunity + "" +
                   i.Address.TowerName + "" +
                   i.Address.District + "" +
                   i.Address.City + "" +
                   i.Address.State + "" +
                   i.Address.Country + "" +
                   i.Address.PostalCode + "").ToLower().Trim().Replace(" ", "")));
            }

            if (request?.Facing != null)
            {
                switch (request.Facing)
                {
                    case Facing.North:
                        query = query.Where(i => i.Facing == Facing.North);
                        break;

                    case Facing.East:
                        query = query.Where(i => i.Facing == Facing.East);
                        break;
                    case Facing.West:
                        query = query.Where(i => i.Facing == Facing.West);
                        break;

                    case Facing.South:
                        query = query.Where(i => i.Facing == Facing.South);
                        break;

                    case Facing.SouthEast:
                        query = query.Where(i => i.Facing == Facing.SouthEast);
                        break;

                    case Facing.SouthWest:
                        query = query.Where(i => i.Facing == Facing.SouthWest);
                        break;

                    case Facing.NorthEast:
                        query = query.Where(i => i.Facing == Facing.NorthEast);
                        break;

                    case Facing.NorthWest:
                        query = query.Where(i => i.Facing == Facing.NorthWest);
                        break;
                }
            }
            if (request?.Facings != null)
            {
                query = query.Where(i => i.Facings != null && i.Facings.Any(i => request.Facings.Contains(i)));
            }

            if (request?.BuilderName?.Any() ?? false)
            {
                query = query.Where(i => i.BuilderDetail != null && request.BuilderName.Contains(i.BuilderDetail.Name));
            }

            if (request.CarpetArea != null && request.CarpetAreaUnitId != default)
            {
                query = query.Where(i => i.AreaUnitId != null && i.Area == request.CarpetArea && i.AreaUnitId == request.CarpetAreaUnitId);
            }
            else if (request.CarpetArea != null)
            {
                query = query.Where(i => i.Area == request.CarpetArea);
            }
            if (request?.Possesion != null)
            {
                switch (request?.Possesion)
                {
                    case PossesionType.UnderConstruction:
                        query = query.Where(i => i.PossesionType == PossesionType.UnderConstruction);
                        break;

                    case PossesionType.SixMonth:
                        query = query.Where(i => i.PossesionType == PossesionType.SixMonth);
                        break;

                    case PossesionType.Year:
                        query = query.Where(i => i.PossesionType == PossesionType.Year);
                        break;

                    case PossesionType.TwoYears:
                        query = query.Where(i => i.PossesionType == PossesionType.TwoYears);
                        break;

                    case PossesionType.CustomDate:
                        DateTime? tempToPossesionDate = request?.ToPossesionDate?.ConvertToDateToUtc();
                        DateTime? tempFrompossesionDate = request?.FromPossesionDate?.ConvertFromDateToUtc();
                        query = query.Where(i => i.PossessionDate >= tempFrompossesionDate && i.PossessionDate <= tempToPossesionDate);
                        break;
                }
            }

            if (request?.AmenitesIds?.Any() ?? false)
            {
                query = query.Where(i => (i.Amenities != null && i.Amenities.Any(i => request.AmenitesIds.Contains(i.MasterProjectAmenityId))));
            }
            if (request?.Currency != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.Currency == request.Currency);
            }
            if (request.MinPrice != null || request.MaxPrice != null)
            {
                if (request.MinPrice != null && request.MaxPrice != null)
                {
                    query = query.Where(i => i.MinimumPrice >= request.MinPrice && i.MaximumPrice <= request.MaxPrice);
                }
                else if (request.MinPrice != null && request.MaxPrice == null)
                {
                    query = query.Where(i => i.MinimumPrice >= request.MinPrice);
                }
                else if (request.MinPrice == null && request.MaxPrice != null)
                {
                    query = query.Where(i => i.MaximumPrice <= request.MaxPrice);
                }
            }
            if (request.FromMinPrice != null || request.ToMinPrice != null || request.FromMaxPrice != null || request.ToMaxPrice != null)
            {
                if (request.FromMinPrice != null && request.ToMinPrice != null)
                {
                    query = query.Where(i => i.MinimumPrice >= request.FromMinPrice && i.MinimumPrice <= request.ToMinPrice);
                }
                else if (request.FromMinPrice != null && request.ToMinPrice == null)
                {
                    query = query.Where(i => i.MinimumPrice >= request.FromMinPrice);
                }
                else if (request.FromMinPrice == null && request.ToMinPrice != null)
                {
                    query = query.Where(i => i.MinimumPrice <= request.ToMinPrice);
                }

                if (request.FromMaxPrice != null && request.ToMaxPrice != null)
                {
                    query = query.Where(i => i.MaximumPrice >= request.FromMaxPrice && i.MaximumPrice <= request.ToMaxPrice);
                }
                else if (request.FromMaxPrice != null && request.ToMaxPrice == null)
                {
                    query = query.Where(i => i.MaximumPrice >= request.FromMaxPrice);
                }
                else if (request.FromMaxPrice == null && request.ToMaxPrice != null)
                {
                    query = query.Where(i => i.MaximumPrice <= request.ToMaxPrice);
                }
            }
            if ((request.MinCarpetArea != null || request.MaxCarpetArea != null) && request.CarpetAreaUnitId != default)
            {
                query = query.Where(i => i.AreaUnitId != null
                    && (request.MinCarpetArea == null || i.Area >= request.MinCarpetArea)
                    && (request.MaxCarpetArea == null || i.Area <= request.MaxCarpetArea)
                    && i.AreaUnitId == request.CarpetAreaUnitId);
            }

            else if (request.MinCarpetArea != null || request.MaxCarpetArea != null)
            {
                query = query.Where(i =>
                    (request.MinCarpetArea == null || i.Area >= request.MinCarpetArea) &&
                    (request.MaxCarpetArea == null || i.Area <= request.MaxCarpetArea));
            }
            if (projectIds?.Any() ?? false)
            {
                query = query.Where(i => projectIds.Contains(i.Id));
            }

            return query;
        }

        private IQueryable<Lrb.Domain.Entities.Project> BuildQueryForMobile(Lrb.Application.Project.Mobile.GetAllProjectRequest request, List<Guid> projectIds)
        {
            var scope = _provider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            if ((request.MinLeadCount != null || request.MaxLeadCount != null ||
                 request.MinProspectCount != null || request.MaxProspectCount != null)
                && (projectIds?.Any() != true))
            {
                return context.Projects.Where(_ => false);
            }
            IQueryable<Lrb.Domain.Entities.Project>? query = null;
            query = context.Projects.Where(i => !i.IsDeleted)
             .Include(i => i.Amenities)
             .Include(i => i.ProjectGalleries)
             .Include(i => i.MonetaryInfo)
             .Include(i => i.ProjectType)
             .Include(i => i.Address)
                 .ThenInclude(i => i.Location)
                     .ThenInclude(i => i.Zone)
                         .ThenInclude(i => i.City)
             .Include(i => i.BuilderDetail)
             .Include(i => i.UserAssignment)
             .Include(i => i.AssociatedBanks)
             .OrderBy(i => i.CurrentStatus)
             .ThenByDescending(i => i.LastModifiedOn - new DateTime(2000, 01, 01, 00, 00, 00, DateTimeKind.Utc)).AsQueryable();
            switch (request.ProjectVisiblity)
            {
                case Lrb.Application.Project.Mobile.ProjectVisiblityType.All:
                    query = query.Where(i => !i.IsArchived);
                    break;

                case Lrb.Application.Project.Mobile.ProjectVisiblityType.Residential:
                    query = query.Where(i => !i.IsArchived).Where(j => (j.ProjectType.Id == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c")) ||
                                                               (j.ProjectType.BaseId == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c")));
                    break;

                case Lrb.Application.Project.Mobile.ProjectVisiblityType.Commercial:
                    query = query.Where(i => !i.IsArchived).Where(j => (j.ProjectType.Id == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573")) ||
                                                               (j.ProjectType.BaseId == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573")));
                    break;

                case Lrb.Application.Project.Mobile.ProjectVisiblityType.Agriculture:
                    query = query.Where(i => !i.IsArchived).Where(j => (j.ProjectType.Id == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8")) ||
                                                               (j.ProjectType.BaseId == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8")));
                    break;

                case Lrb.Application.Project.Mobile.ProjectVisiblityType.Deleted:
                    query = query.Where(i => i.IsArchived);
                    break;
            }

            if (!string.IsNullOrWhiteSpace(request.Search))
            {
                request.Search = request.Search.ToLower().Trim();

                query = query.Where(i => (i.Name.ToLower().Trim().Contains(request.Search)) ||
                    (i.Certificates.ToLower().Trim().Contains(request.Search)) || (i.Address.SubLocality.ToLower().Trim().Contains(request.Search)) ||
                    (i.Address.City.ToLower().Trim().Contains(request.Search)) || (i.Address.Locality.ToLower().Trim().Contains(request.Search)) ||
                    (i.Address.State.ToLower().Trim().Contains(request.Search)) || (i.Address.Country.ToLower().Trim().Contains(request.Search)) ||
                    (i.Address.PostalCode.ToLower().Trim().Contains(request.Search)) || (i.ProjectType.DisplayName.ToLower().Trim().Contains(request.Search)) ||
                    (i.SerialNo.ToLower().Contains(request.Search)));
            }

            if (request.ProjectStatus != null)
            {
                query = query.Where(i => i.Status == request.ProjectStatus);
            }

            if (request.CurrentStatus != null)
            {
                query = query.Where(i => i.CurrentStatus == request.CurrentStatus);
            }

            if (request.ProjectType?.Any() ?? false)
            {
                query = query.Where(i => request.ProjectType.Contains(i.ProjectType.Id) || request.ProjectType.Contains(i.ProjectType.BaseId ?? Guid.Empty));
            }

            if (request.ProjectSubType?.Any() ?? false)
            {
                query = query.Where(i => request.ProjectSubType.Contains(i.ProjectType.Id));
            }

            if ((request.FromDate != null && request.FromDate != default) || (request.ToDate != null && request.ToDate != default))
            {
                DateTime? tempToDate = request?.ToDate?.ConvertToDateToUtc();
                DateTime? tempFromDate = request?.FromDate?.ConvertFromDateToUtc();

                if ((request?.FromDate != null && request.FromDate != default) && (request.ToDate != null && request.ToDate != default))
                {
                    query = query.Where(i => i.StartDate >= tempFromDate && i.EndDate <= tempToDate);
                }
                else if ((request?.FromDate != null && request.FromDate != default) && (request.ToDate == null && request.ToDate == default))
                {
                    query = query.Where(i => i.StartDate >= tempFromDate);
                }
                else if ((request?.FromDate == null && request?.FromDate == default) && (request?.ToDate != null && request.ToDate != default))
                {
                    query = query.Where(i => i.EndDate <= tempToDate);
                }
            }
            if (request?.Locations != null && request.Locations.Any())
            {
                request.Locations = request.Locations.ConvertAll(i => i.Replace(",", "").ToLower().Trim().Replace(" ", "")).ToList();
                query = query.Where(i => request.Locations.Contains((i.Address.SubLocality + "" +
                   i.Address.Locality + "" +
                   i.Address.Community + "" +
                   i.Address.SubCommunity + "" +
                   i.Address.TowerName + "" +
                   i.Address.District + "" +
                   i.Address.City + "" +
                   i.Address.State + "" +
                   i.Address.Country + "" +
                   i.Address.PostalCode + "").ToLower().Trim().Replace(" ", "")));
            }

            if (request?.Possesion != null)
            {
                switch (request?.Possesion)
                {
                    case PossesionType.UnderConstruction:
                        query = query.Where(i => i.PossesionType == PossesionType.UnderConstruction);
                        break;

                    case PossesionType.SixMonth:
                        query = query.Where(i => i.PossesionType == PossesionType.SixMonth);
                        break;

                    case PossesionType.Year:
                        query = query.Where(i => i.PossesionType == PossesionType.Year);
                        break;

                    case PossesionType.TwoYears:
                        query = query.Where(i => i.PossesionType == PossesionType.TwoYears);
                        break;

                    case PossesionType.CustomDate:
                        DateTime? tempToPossesionDate = request?.ToPossesionDate?.ConvertToDateToUtc();
                        DateTime? tempFrompossesionDate = request?.FromPossesionDate?.ConvertFromDateToUtc();
                        query = query.Where(i => i.PossessionDate >= tempFrompossesionDate && i.PossessionDate <= tempToPossesionDate);
                        break;
                }
            }

            if (request?.BuilderName?.Any() ?? false)
            {
                query = query.Where(i => i.BuilderDetail != null && request.BuilderName.Contains(i.BuilderDetail.Name));
            }

            if (request?.Facing != null)
            {
                switch (request.Facing)
                {
                    case Facing.North:
                        query = query.Where(i => i.Facing == Facing.North);
                        break;

                    case Facing.East:
                        query = query.Where(i => i.Facing == Facing.East);
                        break;
                    case Facing.West:
                        query = query.Where(i => i.Facing == Facing.West);
                        break;

                    case Facing.South:
                        query = query.Where(i => i.Facing == Facing.South);
                        break;

                    case Facing.SouthEast:
                        query = query.Where(i => i.Facing == Facing.SouthEast);
                        break;

                    case Facing.SouthWest:
                        query = query.Where(i => i.Facing == Facing.SouthWest);
                        break;

                    case Facing.NorthEast:
                        query = query.Where(i => i.Facing == Facing.NorthEast);
                        break;

                    case Facing.NorthWest:
                        query = query.Where(i => i.Facing == Facing.NorthWest);
                        break;
                }
            }

            if (request?.Facings != null)
            {
                query = query.Where(i => i.Facings != null && i.Facings.Any(i => request.Facings.Contains(i)));
            }

            if (request?.Budgets != null && request.Budgets.Any())
            {
                foreach (var budget in request.Budgets)
                {
                    switch (budget)
                    {
                        case Budget.UpToTenLakhs:
                            query = query.Where(i => i.MaximumPrice != null && i.MaximumPrice <= 1000000);
                            break;
                        case Budget.TenToTwentyLakhs:
                            query = query.Where(i => i.MaximumPrice != null && i.MaximumPrice > 1000000 && i.MaximumPrice <= 2000000);
                            break;
                        case Budget.TwentyToThirtyLakhs:
                            query = query.Where(i => i.MaximumPrice != null && i.MaximumPrice > 2000000 && i.MaximumPrice <= 3000000);
                            break;
                        case Budget.ThirtyToFourtyLakhs:
                            query = query.Where(i => i.MaximumPrice != null && i.MaximumPrice > 3000000 && i.MaximumPrice <= 4000000);
                            break;
                        case Budget.FourtyToFiftyLakhs:
                            query = query.Where(i => i.MaximumPrice != null && i.MaximumPrice > 4000000 && i.MaximumPrice <= 5000000);
                            break;
                        case Budget.FiftyToOneCrore:
                            query = query.Where(i => i.MaximumPrice != null && i.MaximumPrice > 5000000 && i.MaximumPrice <= 10000000);
                            break;
                        case Budget.MoreThanOneCrore:
                            query = query.Where(i => i.MaximumPrice != null && i.MaximumPrice > 10000000);
                            break;
                    }
                }
            }

            if (request?.AmenitesIds?.Any() ?? false)
            {
                query = query.Where(i => (i.Amenities != null && i.Amenities.Any(i => request.AmenitesIds.Contains(i.MasterProjectAmenityId))));
            }
            if (request?.Currency != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.Currency == request.Currency);
            }
            if (request.MinPrice != null || request.MaxPrice != null)
            {
                if (request.MinPrice != null && request.MaxPrice != null)
                {
                    query = query.Where(i => i.MinimumPrice >= request.MinPrice && i.MaximumPrice <= request.MaxPrice);
                }
                else if (request.MinPrice != null && request.MaxPrice == null)
                {
                    query = query.Where(i => i.MinimumPrice >= request.MinPrice);
                }
                else if (request.MinPrice == null && request.MaxPrice != null)
                {
                    query = query.Where(i => i.MaximumPrice <= request.MaxPrice);
                }
            }

            if (request.FromMinPrice != null || request.ToMinPrice != null || request.FromMaxPrice != null || request.ToMaxPrice != null)
            {
                if (request.FromMinPrice != null && request.ToMinPrice != null)
                {
                    query = query.Where(i => i.MinimumPrice >= request.FromMinPrice && i.MinimumPrice <= request.ToMinPrice);
                }
                else if (request.FromMinPrice != null && request.ToMinPrice == null)
                {
                    query = query.Where(i => i.MinimumPrice >= request.FromMinPrice);
                }
                else if (request.FromMinPrice == null && request.ToMinPrice != null)
                {
                    query = query.Where(i => i.MinimumPrice <= request.ToMinPrice);
                }

                if (request.FromMaxPrice != null && request.ToMaxPrice != null)
                {
                    query = query.Where(i => i.MaximumPrice >= request.FromMaxPrice && i.MaximumPrice <= request.ToMaxPrice);
                }
                else if (request.FromMaxPrice != null && request.ToMaxPrice == null)
                {
                    query = query.Where(i => i.MaximumPrice >= request.FromMaxPrice);
                }
                else if (request.FromMaxPrice == null && request.ToMaxPrice != null)
                {
                    query = query.Where(i => i.MaximumPrice <= request.ToMaxPrice);
                }
            }
            if ((request.MinCarpetArea != null || request.MaxCarpetArea != null) && request.CarpetAreaUnitId != default)
            {
                query = query.Where(i => i.AreaUnitId != null
                    && (request.MinCarpetArea == null || i.Area >= request.MinCarpetArea)
                    && (request.MaxCarpetArea == null || i.Area <= request.MaxCarpetArea)
                    && i.AreaUnitId == request.CarpetAreaUnitId);
            }

            else if (request.MinCarpetArea != null || request.MaxCarpetArea != null)
            {
                query = query.Where(i =>
                    (request.MinCarpetArea == null || i.Area >= request.MinCarpetArea) &&
                    (request.MaxCarpetArea == null || i.Area <= request.MaxCarpetArea));
            }

            if (projectIds?.Any() ?? false)
            {
                query = query.Where(i => projectIds.Contains(i.Id));
            }
            return query;
        }
        public async Task<IEnumerable<Lrb.Domain.Entities.Project>> GetAllProjectsExportForWebNewAsync(RunAWSBatchForExportProjectsRequest request, List<Guid> projectIds)
        {
            var query = BuildQueryForExport(request, projectIds);
            try
            {
                var minimalQuery = query
                   .Select(p => new Project
                   {
                       // Only select the fields you actually need
                       Id = p.Id,
                       Name = p.Name,
                       Certificates = p.Certificates,
                       Facing = p.Facing,
                       ReraNumber = p.ReraNumber,
                       Description = p.Description,
                       TotalFlats = p.TotalFlats,
                       TotalFloor = p.TotalFloor,
                       TotalBlocks = p.TotalBlocks,
                       Area = p.Area,
                       AreaUnitId = p.AreaUnitId,
                       Status = p.Status,
                       CurrentStatus = p.CurrentStatus,
                       StartDate = p.StartDate,
                       EndDate = p.EndDate,
                       PossessionDate = p.PossessionDate,
                       MinimumPrice = p.MinimumPrice,
                       MaximumPrice = p.MaximumPrice,
                       PossesionType = p.PossesionType,
                       Facings = p.Facings,
                       LastModifiedOn = p.LastModifiedOn,
                       MonetaryInfo = p.MonetaryInfo != null
                           ? new ProjectMonetaryInfo
                           {
                               Brokerage = p.MonetaryInfo.Brokerage,
                           }
                           : null,
                       BuilderDetail = p.BuilderDetail != null
                           ? new ProjectBuilderDetails
                           {
                               Name = p.BuilderDetail.Name,
                               ContactNo = p.BuilderDetail.ContactNo
                           }
                           : null,
                       Address = p.Address != null
                          ? new Address
                          {
                              SubLocality = p.Address.SubLocality,
                              Locality = p.Address.Locality,
                              City = p.Address.City,
                              State = p.Address.State,
                              Country = p.Address.Country,
                              PostalCode = p.Address.PostalCode,
                              Community = p.Address.Community,
                              SubCommunity = p.Address.SubCommunity,
                              TowerName = p.Address.TowerName
                          } : null,
                       UserAssignment = p.UserAssignment != null
                           ? new UserAssignment
                           {
                               UserIds = p.UserAssignment.UserIds
                           }
                           : null,
                       Amenities = p.Amenities
                           .Select(pa => new ProjectAmenity
                           {
                               MasterProjectAmenityId = pa.MasterProjectAmenityId,
                           }).ToList(),
                       ProjectType = p.ProjectType != null
                           ? new MasterProjectType
                           {
                               DisplayName = p.ProjectType.DisplayName,
                               BaseId = p.ProjectType.BaseId,
                           }
                           : null,
                       AssociatedBanks = p.AssociatedBanks
                           .Select(pa => new AssociatedBank
                           {
                               MasterAssociatedBankId = pa.MasterAssociatedBankId,
                           }).ToList(),

                   });
                var projects = await minimalQuery.ToListAsync();
                return projects;
            }
            catch (Exception e)
            {
                throw;
            }
        }
        private IQueryable<Lrb.Domain.Entities.Project> BuildQueryForExport(RunAWSBatchForExportProjectsRequest request, List<Guid>? projectIds)
        {
            var scope = _provider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

            if ((request.MinLeadCount != null || request.MaxLeadCount != null ||
                 request.MinProspectCount != null || request.MaxProspectCount != null)
                && (projectIds?.Any() != true))
            {
                return context.Projects.Where(_ => false);
            }
            IQueryable<Lrb.Domain.Entities.Project>? query = null;
            query = context.Projects
                .Where(i => !i.IsDeleted)
                .OrderBy(i => i.CurrentStatus)
                .ThenByDescending(i => i.LastModifiedOn - new DateTime(2000, 01, 01, 00, 00, 00, DateTimeKind.Utc)).AsQueryable();
            switch (request.ProjectVisibility)
            {
                case ProjectVisibilityType.All:
                    query = query.Where(i => !i.IsArchived);
                    break;

                case ProjectVisibilityType.Residential:
                    query = query.Where(i => !i.IsArchived).Where(j => (j.ProjectType.Id == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c")) ||
                                                                       (j.ProjectType.BaseId == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c")));
                    break;

                case ProjectVisibilityType.Commercial:
                    query = query.Where(i => !i.IsArchived).Where(j => (j.ProjectType.Id == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573")) ||
                                                                       (j.ProjectType.BaseId == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573")));
                    break;

                case ProjectVisibilityType.Agriculture:
                    query = query.Where(i => !i.IsArchived).Where(j => (j.ProjectType.Id == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8")) ||
                                                                       (j.ProjectType.BaseId == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8")));
                    break;

                case ProjectVisibilityType.Deleted:
                    query = query.Where(i => i.IsArchived);
                    break;
            }

            if (!string.IsNullOrWhiteSpace(request.Search))
            {
                request.Search = request.Search.ToLower().Trim();

                query = query.Where(i => (i.Name.ToLower().Trim().Contains(request.Search)) || (i.Certificates.ToLower().Trim().Contains(request.Search)) ||
                    (i.Address.SubLocality.ToLower().Trim().Contains(request.Search)) ||
                    (i.Address.City.ToLower().Trim().Contains(request.Search)) || (i.Address.Locality.ToLower().Trim().Contains(request.Search)) ||
                    (i.Address.State.ToLower().Trim().Contains(request.Search)) || (i.Address.Country.ToLower().Trim().Contains(request.Search)) ||
                    (i.Address.PostalCode.ToLower().Trim().Contains(request.Search)) || (i.ProjectType.DisplayName.ToLower().Trim().Contains(request.Search)));
            }

            if (request.ProjectStatuses?.Any() ?? false)
            {
                query = query.Where(i => request.ProjectStatuses.Contains(i.Status));
            }

            if (request.CurrentStatus != null)
            {
                query = query.Where(i => i.CurrentStatus == request.CurrentStatus);
            }

            if (request.ProjectType != null)
            {
                switch (request.ProjectType)
                {
                    case Lrb.Application.Project.Web.Requests.ProjectType.All:
                        break;

                    case Lrb.Application.Project.Web.Requests.ProjectType.Residential:
                        query = query.Where(j => (j.ProjectType.Id == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c")) ||
                                                                       (j.ProjectType.BaseId == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c")));
                        break;

                    case Lrb.Application.Project.Web.Requests.ProjectType.Commercial:
                        query = query.Where(j => (j.ProjectType.Id == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573")) ||
                                                                       (j.ProjectType.BaseId == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573")));
                        break;

                    case Lrb.Application.Project.Web.Requests.ProjectType.Agricultural:
                        query = query.Where(j => (j.ProjectType.Id == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8")) ||
                                                                       (j.ProjectType.BaseId == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8")));
                        break;
                }
            }

            if (request.ProjectSubType?.Any() ?? false)
            {
                query = query.Where(i => request.ProjectSubType.Contains(i.ProjectType.Id));
            }

            if (request.MinPrice != null || request.MaxPrice != null)
            {
                if (request.MinPrice != null && request.MaxPrice != null)
                {
                    query = query.Where(i => i.MinimumPrice >= request.MinPrice && i.MaximumPrice <= request.MaxPrice);
                }
                else if (request.MinPrice != null && request.MaxPrice == null)
                {
                    query = query.Where(i => i.MinimumPrice >= request.MinPrice);
                }
                else if (request.MinPrice == null && request.MaxPrice != null)
                {
                    query = query.Where(i => i.MaximumPrice <= request.MaxPrice);
                }
            }

            if ((request.FromDate != null && request.FromDate != default) || (request.ToDate != null && request.ToDate != default))
            {
                DateTime? tempToDate = request?.ToDate?.ConvertToDateToUtc();
                DateTime? tempFromDate = request?.FromDate?.ConvertFromDateToUtc();

                if ((request?.FromDate != null && request.FromDate != default) && (request.ToDate != null && request.ToDate != default))
                {
                    query = query.Where(i => i.StartDate >= tempFromDate && i.EndDate <= tempToDate);
                }
                else if ((request?.FromDate != null && request.FromDate != default) && (request.ToDate == null && request.ToDate == default))
                {
                    query = query.Where(i => i.StartDate >= tempFromDate);
                }
                else if ((request?.FromDate == null && request?.FromDate == default) && (request?.ToDate != null && request.ToDate != default))
                {
                    query = query.Where(i => i.EndDate <= tempToDate);
                }
            }
            if (request?.Locations != null && request.Locations.Any())
            {
                request.Locations = request.Locations.ConvertAll(i => i.Replace(",", "").ToLower().Trim().Replace(" ", "")).ToList();
                query = query.Where(i => request.Locations.Contains((i.Address.SubLocality + "" +
                   i.Address.Locality + "" +
                   i.Address.Community + "" +
                   i.Address.SubCommunity + "" +
                   i.Address.TowerName + "" +
                   i.Address.District + "" +
                   i.Address.City + "" +
                   i.Address.State + "" +
                   i.Address.Country + "" +
                   i.Address.PostalCode + "").ToLower().Trim().Replace(" ", "")));
            }

            if (request?.Facing != null)
            {
                switch (request.Facing)
                {
                    case Facing.North:
                        query = query.Where(i => i.Facing == Facing.North);
                        break;

                    case Facing.East:
                        query = query.Where(i => i.Facing == Facing.East);
                        break;
                    case Facing.West:
                        query = query.Where(i => i.Facing == Facing.West);
                        break;

                    case Facing.South:
                        query = query.Where(i => i.Facing == Facing.South);
                        break;

                    case Facing.SouthEast:
                        query = query.Where(i => i.Facing == Facing.SouthEast);
                        break;

                    case Facing.SouthWest:
                        query = query.Where(i => i.Facing == Facing.SouthWest);
                        break;

                    case Facing.NorthEast:
                        query = query.Where(i => i.Facing == Facing.NorthEast);
                        break;

                    case Facing.NorthWest:
                        query = query.Where(i => i.Facing == Facing.NorthWest);
                        break;
                }
            }
            if (request?.Facings != null)
            {
                query = query.Where(i => i.Facings != null && i.Facings.Any(i => request.Facings.Contains(i)));
            }

            if (request?.BuilderName?.Any() ?? false)
            {
                query = query.Where(i => i.BuilderDetail != null && request.BuilderName.Contains(i.BuilderDetail.Name));
            }

            if (request.CarpetArea != null && request.CarpetAreaUnitId != default)
            {
                query = query.Where(i => i.AreaUnitId != null && i.Area == request.CarpetArea && i.AreaUnitId == request.CarpetAreaUnitId);
            }
            else if (request.CarpetArea != null)
            {
                query = query.Where(i => i.Area == request.CarpetArea);
            }
            if (request?.Possesion != null)
            {
                switch (request?.Possesion)
                {
                    case PossesionType.UnderConstruction:
                        query = query.Where(i => i.PossesionType == PossesionType.UnderConstruction);
                        break;

                    case PossesionType.SixMonth:
                        query = query.Where(i => i.PossesionType == PossesionType.SixMonth);
                        break;

                    case PossesionType.Year:
                        query = query.Where(i => i.PossesionType == PossesionType.Year);
                        break;

                    case PossesionType.TwoYears:
                        query = query.Where(i => i.PossesionType == PossesionType.TwoYears);
                        break;

                    case PossesionType.CustomDate:
                        DateTime? tempToPossesionDate = request?.ToPossesionDate?.ConvertToDateToUtc();
                        DateTime? tempFrompossesionDate = request?.FromPossesionDate?.ConvertFromDateToUtc();
                        query = query.Where(i => i.PossessionDate >= tempFrompossesionDate && i.PossessionDate <= tempToPossesionDate);
                        break;
                }
            }

            if (request?.AmenitesIds?.Any() ?? false)
            {
                query = query.Where(i => (i.Amenities != null && i.Amenities.Any(i => request.AmenitesIds.Contains(i.MasterProjectAmenityId))));
            }
            if (request?.Currency != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.Currency == request.Currency);
            }
            if (request.MinPrice != null || request.MaxPrice != null)
            {
                if (request.MinPrice != null && request.MaxPrice != null)
                {
                    query = query.Where(i => i.MinimumPrice >= request.MinPrice && i.MaximumPrice <= request.MaxPrice);
                }
                else if (request.MinPrice != null && request.MaxPrice == null)
                {
                    query = query.Where(i => i.MinimumPrice >= request.MinPrice);
                }
                else if (request.MinPrice == null && request.MaxPrice != null)
                {
                    query = query.Where(i => i.MaximumPrice <= request.MaxPrice);
                }
            }
            if (request.FromMinPrice != null || request.ToMinPrice != null || request.FromMaxPrice != null || request.ToMaxPrice != null)
            {
                if (request.FromMinPrice != null && request.ToMinPrice != null)
                {
                    query = query.Where(i => i.MinimumPrice >= request.FromMinPrice && i.MinimumPrice <= request.ToMinPrice);
                }
                else if (request.FromMinPrice != null && request.ToMinPrice == null)
                {
                    query = query.Where(i => i.MinimumPrice >= request.FromMinPrice);
                }
                else if (request.FromMinPrice == null && request.ToMinPrice != null)
                {
                    query = query.Where(i => i.MinimumPrice <= request.ToMinPrice);
                }

                if (request.FromMaxPrice != null && request.ToMaxPrice != null)
                {
                    query = query.Where(i => i.MaximumPrice >= request.FromMaxPrice && i.MaximumPrice <= request.ToMaxPrice);
                }
                else if (request.FromMaxPrice != null && request.ToMaxPrice == null)
                {
                    query = query.Where(i => i.MaximumPrice >= request.FromMaxPrice);
                }
                else if (request.FromMaxPrice == null && request.ToMaxPrice != null)
                {
                    query = query.Where(i => i.MaximumPrice <= request.ToMaxPrice);
                }
            }
            if ((request.MinCarpetArea != null || request.MaxCarpetArea != null) && request.CarpetAreaUnitId != default)
            {
                query = query.Where(i => i.AreaUnitId != null
                    && (request.MinCarpetArea == null || i.Area >= request.MinCarpetArea)
                    && (request.MaxCarpetArea == null || i.Area <= request.MaxCarpetArea)
                    && i.AreaUnitId == request.CarpetAreaUnitId);
            }

            else if (request.MinCarpetArea != null || request.MaxCarpetArea != null)
            {
                query = query.Where(i =>
                    (request.MinCarpetArea == null || i.Area >= request.MinCarpetArea) &&
                    (request.MaxCarpetArea == null || i.Area <= request.MaxCarpetArea));
            }
            if (projectIds?.Any() ?? false)
            {
                query = query.Where(i => projectIds.Contains(i.Id));
            }

            return query;
        }
        public async Task<IEnumerable<Lrb.Domain.Entities.Project>> GetAnonymousProjectsForWebAsync(GetAllProjectAnonymousRequest request)
        {
            var query = BuildQuery(request);
            query = query.Skip(request.PageSize * (request.PageNumber - 1))
              .Take(request.PageSize);
            try
            {
                var minimalQuery = query
                   .Select(p => new Project
                   {
                       // Only select the fields you actually need
                       Id = p.Id,
                       Name = p.Name,
                       Certificates = p.Certificates,
                       ReraNumber = p.ReraNumber,
                       Description = p.Description,
                       TotalFlats = p.TotalFlats,
                       TotalFloor = p.TotalFloor,
                       TotalBlocks = p.TotalBlocks,
                       Area = p.Area,
                       Status = p.Status,
                       CurrentStatus = p.CurrentStatus,
                       StartDate = p.StartDate,
                       EndDate = p.EndDate,
                       PossessionDate = p.PossessionDate,
                       Brochures = p.Brochures,
                       IsGlobal =p.IsGlobal,
                       IsArchived =p.IsArchived,
                       ArchivedOn =p.ArchivedOn,
                       ArchivedBy = p.ArchivedBy,
                       MinimumPrice = p.MinimumPrice,
                       MaximumPrice = p.MaximumPrice,
                       PossesionType = p.PossesionType,
                       Facings = p.Facings,
                       Links = p.Links,
                       IsWaterMarkEnabled = p.IsWaterMarkEnabled,
                       LastModifiedOn = p.LastModifiedOn,
                       CreatedOn = p.CreatedOn,
                       SerialNo = p.SerialNo,
                       ContactRecords = p.ContactRecords,
                       Address = p.Address != null
                                 ? new Address
                                 {
                                     Id = p.Address.Id,
                                     PlaceId = p.Address.PlaceId,
                                     SubLocality = p.Address.SubLocality,
                                     Locality = p.Address.Locality,
                                     District = p.Address.District,
                                     City = p.Address.City,
                                     State = p.Address.State,
                                     Country = p.Address.Country,
                                     PostalCode = p.Address.PostalCode,
                                     Longitude = p.Address.Longitude,
                                     Latitude = p.Address.Latitude,
                                     IsGoogleMapLocation = p.Address.IsGoogleMapLocation,
                                     LocationId = p.Address.LocationId,
                                     Community = p.Address.Community,
                                     SubCommunity = p.Address.SubCommunity,
                                     TowerName = p.Address.TowerName,
                                     Location = p.Address.Location,
                                 } : null,

                       MonetaryInfo = p.MonetaryInfo != null
                           ? new ProjectMonetaryInfo
                           {
                               Id = p.MonetaryInfo.Id,
                               Brokerage = p.MonetaryInfo.Brokerage,
                               BrokerageUnit = p.MonetaryInfo.BrokerageUnit,
                               Currency = p.MonetaryInfo.Currency,
                               BrokerageCurrency = p.MonetaryInfo.BrokerageCurrency,
                               MaintenanceCost = p.MonetaryInfo.MaintenanceCost,
                           }
                           : null,

                       BuilderDetail = p.BuilderDetail != null
                           ? new ProjectBuilderDetails
                           {
                               Name = p.BuilderDetail.Name,
                               ContactNo = p.BuilderDetail.ContactNo,
                               PointOfContact = p.BuilderDetail.PointOfContact,
                           }
                           : null,
                       UnitTypes = p.UnitTypes
                           .Select(u => new UnitType
                           {
                               Attributes = u.Attributes
                                   .Select(ut => new UnitTypeAttribute
                                   {
                                       MasterProjectUnitAttributeId = ut.MasterProjectUnitAttributeId,
                                   }).ToList(),
                               MasterUnitType = u.MasterUnitType != null
                                   ? new MasterProjectType
                                   {
                                       Id = u.MasterUnitType.Id,
                                       BaseId = u.MasterUnitType.BaseId,
                                       Level = u.MasterUnitType.Level,
                                       Type = u.MasterUnitType.Type,
                                       DisplayName = u.MasterUnitType.DisplayName,
                                   }
                                   : null,
                               UnitInfoGalleries = u.UnitInfoGalleries
                                   .Select(ug => new UnitInfoGallery
                                   {
                                       Name =ug.Name,
                                       ImageFilePath = ug.ImageFilePath,
                                       IsCoverImage = ug.IsCoverImage,
                                       GalleryType = ug.GalleryType,
                                   }).ToList(),
                               ContactRecords = u.ContactRecords,

                           }).ToList(),
                       Blocks = p.Blocks
                           .Select(b => new Block
                           {
                               Id = b.Id,
                               Name = b.Name,
                               Area = b.Area,
                               AreaUnitId = b.AreaUnitId,
                               StartDate = b.StartDate,
                               EndDate = b.EndDate,
                               PossessionDate = b.PossessionDate,
                               PossesionType = b.PossesionType,
                               NumberOfFloors = b.NumberOfFloors
                           }).ToList(),
                       Amenities = p.Amenities
                           .Select(pa => new ProjectAmenity
                           {
                               MasterProjectAmenityId = pa.MasterProjectAmenityId,
                           }).ToList(),
                       ProjectGalleries = p.ProjectGalleries
                                   .Select(ug => new ProjectGallery
                                   {
                                       Name = ug.Name,
                                       ImageFilePath = ug.ImageFilePath,
                                       IsCoverImage = ug.IsCoverImage,
                                       ImageKey = ug.ImageKey,
                                       GalleryType = ug.GalleryType,
                                   }).ToList(),
                       ProjectType = p.ProjectType != null
                           ? new MasterProjectType
                           {
                               Id = p.ProjectType.Id,
                               BaseId = p.ProjectType.BaseId,
                               Level = p.ProjectType.Level,
                               Type = p.ProjectType.Type,
                               DisplayName = p.ProjectType.DisplayName,
                           }
                           : null,



                       UserAssignment = p.UserAssignment != null
                           ? new UserAssignment
                           {
                               UserIds = p.UserAssignment.UserIds
                           }
                           : null,
                      
                       AssociatedBanks = p.AssociatedBanks
                           .Select(pa => new AssociatedBank
                           {
                               MasterAssociatedBankId = pa.MasterAssociatedBankId,
                           }).ToList(),

                   });
                var projects = await minimalQuery.ToListAsync();
                return projects;
            }
            catch (Exception e)
            {
                throw;
            }
        }
        public async Task<int> GetAnonymousProjectsCountForWebAsync(GetAllProjectAnonymousRequest request)
        {
            var query = BuildQuery(request);
            try
            {
                var projects = await query.CountAsync();
                return projects;
            }
            catch (Exception e)
            {
                throw;
            }
        }
        private IQueryable<Lrb.Domain.Entities.Project> BuildQuery(GetAllProjectAnonymousRequest request)
        {
            var scope = _provider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            IQueryable<Lrb.Domain.Entities.Project>? query = null;
            query = context.Projects
                .Where(i => !i.IsDeleted)
                .OrderBy(i => i.CurrentStatus)
                .ThenByDescending(i => i.LastModifiedOn - new DateTime(2000, 01, 01, 00, 00, 00, DateTimeKind.Utc)).AsQueryable();
            switch (request.ProjectVisibility)
            {
                case ProjectVisibilityType.All:
                    query = query.Where(i => !i.IsArchived);
                    break;

                case ProjectVisibilityType.Residential:
                    query = query.Where(i => !i.IsArchived).Where(j => (j.ProjectType.Id == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c")) ||
                                                                       (j.ProjectType.BaseId == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c")));
                    break;

                case ProjectVisibilityType.Commercial:
                    query = query.Where(i => !i.IsArchived).Where(j => (j.ProjectType.Id == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573")) ||
                                                                       (j.ProjectType.BaseId == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573")));
                    break;

                case ProjectVisibilityType.Agriculture:
                    query = query.Where(i => !i.IsArchived).Where(j => (j.ProjectType.Id == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8")) ||
                                                                       (j.ProjectType.BaseId == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8")));
                    break;

                case ProjectVisibilityType.Deleted:
                    query = query.Where(i => i.IsArchived);
                    break;
            }

            if (!string.IsNullOrWhiteSpace(request.Search))
            {
                request.Search = request.Search.ToLower().Trim();

                query = query.Where(i => (i.Name.ToLower().Trim().Contains(request.Search)) || (i.Certificates.ToLower().Trim().Contains(request.Search)) ||
                    (i.Address.SubLocality.ToLower().Trim().Contains(request.Search)) ||
                    (i.Address.City.ToLower().Trim().Contains(request.Search)) || (i.Address.Locality.ToLower().Trim().Contains(request.Search)) ||
                    (i.Address.State.ToLower().Trim().Contains(request.Search)) || (i.Address.Country.ToLower().Trim().Contains(request.Search)) ||
                    (i.Address.PostalCode.ToLower().Trim().Contains(request.Search)) || (i.ProjectType.DisplayName.ToLower().Trim().Contains(request.Search)));
            }

            if (request.ProjectStatuses?.Any() ?? false)
            {
                query = query.Where(i => request.ProjectStatuses.Contains(i.Status));
            }

            if (request.CurrentStatus != null)
            {
                query = query.Where(i => i.CurrentStatus == request.CurrentStatus);
            }

            if (request.ProjectType != null)
            {
                switch (request.ProjectType)
                {
                    case Lrb.Application.Project.Web.Requests.ProjectType.All:
                        break;

                    case Lrb.Application.Project.Web.Requests.ProjectType.Residential:
                        query = query.Where(j => (j.ProjectType.Id == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c")) ||
                                                                       (j.ProjectType.BaseId == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c")));
                        break;

                    case Lrb.Application.Project.Web.Requests.ProjectType.Commercial:
                        query = query.Where(j => (j.ProjectType.Id == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573")) ||
                                                                       (j.ProjectType.BaseId == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573")));
                        break;

                    case Lrb.Application.Project.Web.Requests.ProjectType.Agricultural:
                        query = query.Where(j => (j.ProjectType.Id == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8")) ||
                                                                       (j.ProjectType.BaseId == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8")));
                        break;
                }
            }

            if (request.ProjectSubType?.Any() ?? false)
            {
                query = query.Where(i => request.ProjectSubType.Contains(i.ProjectType.Id));
            }

            if (request.MinPrice != null || request.MaxPrice != null)
            {
                if (request.MinPrice != null && request.MaxPrice != null)
                {
                    query = query.Where(i => i.MinimumPrice >= request.MinPrice && i.MaximumPrice <= request.MaxPrice);
                }
                else if (request.MinPrice != null && request.MaxPrice == null)
                {
                    query = query.Where(i => i.MinimumPrice >= request.MinPrice);
                }
                else if (request.MinPrice == null && request.MaxPrice != null)
                {
                    query = query.Where(i => i.MaximumPrice <= request.MaxPrice);
                }
            }

            if ((request.FromDate != null && request.FromDate != default) || (request.ToDate != null && request.ToDate != default))
            {
                DateTime? tempToDate = request?.ToDate?.ConvertToDateToUtc();
                DateTime? tempFromDate = request?.FromDate?.ConvertFromDateToUtc();

                if ((request?.FromDate != null && request.FromDate != default) && (request.ToDate != null && request.ToDate != default))
                {
                    query = query.Where(i => i.StartDate >= tempFromDate && i.EndDate <= tempToDate);
                }
                else if ((request?.FromDate != null && request.FromDate != default) && (request.ToDate == null && request.ToDate == default))
                {
                    query = query.Where(i => i.StartDate >= tempFromDate);
                }
                else if ((request?.FromDate == null && request?.FromDate == default) && (request?.ToDate != null && request.ToDate != default))
                {
                    query = query.Where(i => i.EndDate <= tempToDate);
                }
            }
            if (request?.Locations != null && request.Locations.Any())
            {
                request.Locations = request.Locations.ConvertAll(i => i.Replace(",", "").ToLower().Trim().Replace(" ", "")).ToList();
                query = query.Where(i => request.Locations.Contains((i.Address.SubLocality + "" +
                   i.Address.Locality + "" +
                   i.Address.Community + "" +
                   i.Address.SubCommunity + "" +
                   i.Address.TowerName + "" +
                   i.Address.District + "" +
                   i.Address.City + "" +
                   i.Address.State + "" +
                   i.Address.Country + "" +
                   i.Address.PostalCode + "").ToLower().Trim().Replace(" ", "")));
            }

            if (request?.Facing != null)
            {
                switch (request.Facing)
                {
                    case Facing.North:
                        query = query.Where(i => i.Facing == Facing.North);
                        break;

                    case Facing.East:
                        query = query.Where(i => i.Facing == Facing.East);
                        break;
                    case Facing.West:
                        query = query.Where(i => i.Facing == Facing.West);
                        break;

                    case Facing.South:
                        query = query.Where(i => i.Facing == Facing.South);
                        break;

                    case Facing.SouthEast:
                        query = query.Where(i => i.Facing == Facing.SouthEast);
                        break;

                    case Facing.SouthWest:
                        query = query.Where(i => i.Facing == Facing.SouthWest);
                        break;

                    case Facing.NorthEast:
                        query = query.Where(i => i.Facing == Facing.NorthEast);
                        break;

                    case Facing.NorthWest:
                        query = query.Where(i => i.Facing == Facing.NorthWest);
                        break;
                }
            }
            if (request?.Facings != null)
            {
                query = query.Where(i => i.Facings != null && i.Facings.Any(i => request.Facings.Contains(i)));
            }

            if (request?.BuilderName?.Any() ?? false)
            {
                query = query.Where(i => i.BuilderDetail != null && request.BuilderName.Contains(i.BuilderDetail.Name));
            }

            if (request.CarpetArea != null && request.CarpetAreaUnitId != default)
            {
                query = query.Where(i => i.AreaUnitId != null && i.Area == request.CarpetArea && i.AreaUnitId == request.CarpetAreaUnitId);
            }
            else if (request.CarpetArea != null)
            {
                query = query.Where(i => i.Area == request.CarpetArea);
            }
            if (request?.Possesion != null)
            {
                switch (request?.Possesion)
                {
                    case PossesionType.UnderConstruction:
                        query = query.Where(i => i.PossesionType == PossesionType.UnderConstruction);
                        break;

                    case PossesionType.SixMonth:
                        query = query.Where(i => i.PossesionType == PossesionType.SixMonth);
                        break;

                    case PossesionType.Year:
                        query = query.Where(i => i.PossesionType == PossesionType.Year);
                        break;

                    case PossesionType.TwoYears:
                        query = query.Where(i => i.PossesionType == PossesionType.TwoYears);
                        break;

                    case PossesionType.CustomDate:
                        DateTime? tempToPossesionDate = request?.ToPossesionDate?.ConvertToDateToUtc();
                        DateTime? tempFrompossesionDate = request?.FromPossesionDate?.ConvertFromDateToUtc();
                        query = query.Where(i => i.PossessionDate >= tempFrompossesionDate && i.PossessionDate <= tempToPossesionDate);
                        break;
                }
            }

            if (request?.AmenitesIds?.Any() ?? false)
            {
                query = query.Where(i => (i.Amenities != null && i.Amenities.Any(i => request.AmenitesIds.Contains(i.MasterProjectAmenityId))));
            }
            if (request?.Currency != null)
            {
                query = query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.Currency == request.Currency);
            }
            if (request.MinPrice != null || request.MaxPrice != null)
            {
                if (request.MinPrice != null && request.MaxPrice != null)
                {
                    query = query.Where(i => i.MinimumPrice >= request.MinPrice && i.MaximumPrice <= request.MaxPrice);
                }
                else if (request.MinPrice != null && request.MaxPrice == null)
                {
                    query = query.Where(i => i.MinimumPrice >= request.MinPrice);
                }
                else if (request.MinPrice == null && request.MaxPrice != null)
                {
                    query = query.Where(i => i.MaximumPrice <= request.MaxPrice);
                }
            }

            return query;
        }
    }
}
