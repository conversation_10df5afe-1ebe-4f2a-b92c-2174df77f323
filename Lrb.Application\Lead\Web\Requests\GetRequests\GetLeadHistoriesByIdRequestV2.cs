﻿using Lrb.Application.Common.Caching;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.Utils;
using Microsoft.Extensions.Logging;

namespace Lrb.Application.Lead.Web.Requests.GetRequests
{
    public class GetLeadHistoriesByIdRequestV2 : IRequest<Response<Dictionary<DateTime, List<LeadHistoryDto>>>>
    {
        public Guid LeadId { get; set; }
        public GetLeadHistoriesByIdRequestV2(Guid id) => LeadId = id;
        public bool? CanAccessAllLeads { get; set; }
    }

    public class GetLeadHistoriesByIdRequestV2Handler : IRequestHandler<GetLeadHistoriesByIdRequestV2, Response<Dictionary<DateTime, List<LeadHistoryDto>>>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.LeadHistoryHot> _leadHistoryRepo;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        private readonly ICacheService _cache;
        private readonly ICacheKeyService _cacheKeys;
        private readonly ILogger<GetLeadHistoriesByIdRequestV2Handler> _logger;
        private readonly IReadRepository<Address> _addressRepo;

        public GetLeadHistoriesByIdRequestV2Handler(
            IRepositoryWithEvents<Domain.Entities.Lead> leadRepo,
            IRepositoryWithEvents<Domain.Entities.LeadHistoryHot> leadHistoryRepo,
            IDapperRepository dapperRepository,
            ICurrentUser currentUser,
            ICacheService cache,
            ICacheKeyService cacheKeys,
            ILogger<GetLeadHistoriesByIdRequestV2Handler> logger,
            IReadRepository<Address> addressRepo)
        {
            _leadRepo = leadRepo;
            _leadHistoryRepo = leadHistoryRepo;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
            _cache = cache;
            _cacheKeys = cacheKeys;
            _logger = logger;
            _addressRepo = addressRepo;
        }

        public async Task<Response<Dictionary<DateTime, List<LeadHistoryDto>>>> Handle(GetLeadHistoriesByIdRequestV2 request, CancellationToken cancellationToken)
        {
            try
            {
                var currentUserId = _currentUser.GetUserId();
                var tenantId = _currentUser.GetTenant();

                // Generate cache key for this specific lead history request
                var cacheKey = _cacheKeys.GetCacheKey("LeadHistoryV2", $"{request.LeadId}_{currentUserId}_{request.CanAccessAllLeads}");

                // Try to get from cache first
                var cachedResult = await _cache.GetAsync<CachedLeadHistoryData>(cacheKey, cancellationToken);

                // Check if we need to refresh the cache by comparing with latest modification time
                var shouldRefreshCache = await ShouldRefreshCacheAsync(request.LeadId, tenantId, cachedResult, cancellationToken);

                if (cachedResult != null && !shouldRefreshCache)
                {
                    _logger.LogDebug("Returning cached lead history for LeadId: {LeadId}", request.LeadId);
                    return new Response<Dictionary<DateTime, List<LeadHistoryDto>>>(cachedResult.HistoryData);
                }

                // Cache miss or data changed - fetch from database
                _logger.LogDebug("Cache miss or data changed for LeadId: {LeadId}, fetching from database", request.LeadId);

                var historyData = await GetLeadHistoryFromDatabaseAsync(request, currentUserId, tenantId, cancellationToken);

                // Cache the result with 1-hour expiration
                var cacheData = new CachedLeadHistoryData
                {
                    HistoryData = historyData.Item1,
                    LastModified = DateTime.UtcNow,
                    LeadId = request.LeadId,
                    LatestVersion = historyData.Item2,
                };

                await _cache.SetAsync(cacheKey, cacheData, TimeSpan.FromHours(8), cancellationToken);
                _logger.LogDebug("Cached lead history for LeadId: {LeadId}", request.LeadId);

                return new Response<Dictionary<DateTime, List<LeadHistoryDto>>>(historyData.Item1);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving lead history for LeadId: {LeadId}", request.LeadId);
                throw;
            }
        }

        private async Task<bool> ShouldRefreshCacheAsync(Guid leadId, string tenantId, CachedLeadHistoryData? cachedData, CancellationToken cancellationToken)
        {
            if (cachedData == null)
                return true;

            try
            {
                // Check if there are any recent changes to LeadHistoryHot table for this lead
                // This provides efficient change detection
                var latestModificationVersion = await _dapperRepository.GetLatestLeadHistoryVersionAsync(leadId, tenantId);

                // If we can't determine the latest modification time, refresh the cache to be safe
                if (latestModificationVersion == null)
                    return true;

                // Refresh if the latest modification is newer than our cached data
                return latestModificationVersion > cachedData.LatestVersion;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error checking cache freshness for LeadId: {LeadId}, refreshing cache", leadId);
                return true; // Refresh cache on error to ensure data consistency
            }
        }

        private async Task<(Dictionary<DateTime, List<LeadHistoryDto>>, int)> GetLeadHistoryFromDatabaseAsync(
            GetLeadHistoriesByIdRequestV2 request,
            Guid currentUserId,
            string tenantId,
            CancellationToken cancellationToken)
        {
            // Get the lead first to validate access
            var lead = await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(request.LeadId, true));
            if (lead == null)
            {
                throw new NotFoundException("Lead not found by this id");
            }

            // Check admin status
            var isAdmin = await _dapperRepository.IsAdminAsync(currentUserId, tenantId);

            List<LeadHistoryHot> leadHistories;
            List<Guid> subIds = new();

            if (isAdmin)
            {
                // Admin can see all history
                leadHistories = await _leadHistoryRepo.ListAsync(new HotLeadHistorySpecV2(request.LeadId));
            }
            else
            {
                // Non-admin users need permission checking
                subIds = (await _dapperRepository.GetSubordinateIdsAsync(currentUserId, tenantId, request?.CanAccessAllLeads, isAdmin))?.ToList() ?? new();
                subIds.AddRange(new List<Guid>() { currentUserId, lead.AssignTo });

                if (lead?.SecondaryUserId != null && lead.SecondaryUserId != Guid.Empty)
                {
                    subIds.Add(lead.SecondaryUserId.Value);
                }

                subIds = subIds?.Where(i => i != Guid.Empty)?.Distinct()?.ToList() ?? new List<Guid>();
                leadHistories = await _leadHistoryRepo.ListAsync(new HotLeadHistorySpecV2(request.LeadId, subIds));
            }

            if (!leadHistories?.Any() ?? true)
            {
                return (null, 0);
            }

            var histories = new List<LeadHistoryHot>();
            foreach (var history in leadHistories)
            {
                if (history.FieldType != typeof(Guid).Name)
                {
                    if (history.FieldName != "Last Modified On" && history.FieldName != "Last Modified User")
                    {
                        if (history.NewValue != null || history.OldValue != null)
                        {
                            if (history.FieldName == "Created On" || history.FieldName == "Schedule Date" || history.FieldName == "Qualified Date")
                            {
                                history.NewValue = GetLocalDateOfHistory(history.NewValue ?? string.Empty);
                                if (!string.IsNullOrEmpty(history.OldValue))
                                {
                                    history.OldValue = GetLocalDateOfHistory(history.OldValue ?? string.Empty);
                                }
                            }
                            histories.Add(history);
                        }
                    }
                }
            }

            var datas = histories.OrderByDescending(i => i.ModifiedOn);
            var maxVersion = datas.Max(i => i.Version);
            histories.OrderByDescending(i => i.ModifiedOn);
            var historyViewModel = new Dictionary<DateTime, List<LeadHistoryHot>>();
            histories.GroupBy(i => i.GroupKey).ToList();
            histories.ForEach(i => i.ModifiedOn = i?.ModifiedOn.Value.ToLocalTime("India Standard Time"));
            var groups = histories.GroupBy(i => i.ModifiedOn).ToList();
            foreach (var group in groups)
            {
                historyViewModel.Add(group.Key ?? DateTime.Now, group.ToList());
            }

            var dto = historyViewModel.Adapt<Dictionary<DateTime, List<LeadHistoryDto>>>();

            // Sort the history items by date (most recent first)
            var sortedHistory = dto.OrderByDescending(i => i.Key).ToDictionary(i => i.Key, j => j.Value.ToList());

            return (sortedHistory, maxVersion);
        }

        public static string? GetLocalDateOfHistory(string date)
        {
            DateTime? parseDate = null;
            if (!string.IsNullOrEmpty(date))
            {
                string[] dateFormats = { "dd-MM-yy HH:mm:ss", "dd-MM-yyyy HH:mm:ss", "dd/MM/yy HH:mm:ss", "dd/MM/yyyy HH:mm:ss", "yyyy-MM-dd HH:mm:ss", "MM/dd/yyyy HH:mm:ss", "dd-MMM-yyyy HH:mm:ss", "dddd, MMMM dd, yyyy HH:mm:ss" };
                if (DateTime.TryParseExact(date, dateFormats, null, System.Globalization.DateTimeStyles.None, out DateTime dateTime))
                {
                    parseDate = dateTime;
                }

                if (parseDate != null)
                {
                    return (DateTimeExtensions.ToIndianStandardTime(parseDate ?? default)).ToString();
                }
                else
                {
                    return null;
                }
            }
            else
            {
                return null;
            }

        }
    }

    /// <summary>
    /// Cached data structure for lead history with change detection
    /// </summary>
    public class CachedLeadHistoryData
    {
        public Dictionary<DateTime, List<LeadHistoryDto>> HistoryData { get; set; } = new();
        public DateTime LastModified { get; set; }
        public Guid LeadId { get; set; }
        public int LatestVersion { get; set; }
    }
}
