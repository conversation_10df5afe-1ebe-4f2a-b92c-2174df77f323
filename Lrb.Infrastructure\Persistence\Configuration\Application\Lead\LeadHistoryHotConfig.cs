﻿using Finbuckle.MultiTenant.EntityFrameworkCore;
using Lrb.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Lrb.Infrastructure.Persistence.Configuration.Application.Lead
{
    public class LeadHistoryHotConfig : IEntityTypeConfiguration<LeadHistoryHot>
    {
        public void Configure(EntityTypeBuilder<LeadHistoryHot> builder)
        {
            builder.IsMultiTenant();
        }
    }

    public class LeadHistoryWarmConfig : IEntityTypeConfiguration<LeadHistoryWarm>
    {
        public void Configure(EntityTypeBuilder<LeadHistoryWarm> builder)
        {
            builder.IsMultiTenant();
        }
    }

    public class LeadHistoryColdConfig : IEntityTypeConfiguration<LeadHistoryCold>
    {
        public void Configure(EntityTypeBuilder<LeadHistoryCold> builder)
        {
            builder.IsMultiTenant();
        }
    }
}
