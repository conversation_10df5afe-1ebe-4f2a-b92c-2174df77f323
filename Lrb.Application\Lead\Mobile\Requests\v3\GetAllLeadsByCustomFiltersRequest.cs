﻿using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.CustomFilters.Mobile;
using Lrb.Application.Lead.Mobile.Dtos.v4;
using System.Collections.Concurrent;

namespace Lrb.Application.Lead.Mobile
{
    public class GetAllLeadsParametersNewFilters : PaginationFilter
    {
        public List<LeadSource>? Source { get; set; } = new();
        public List<EnquiryType>? EnquiredFor { get; set; }
        public Guid? AssignTo { get; set; }
        public DateType? DateType { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string? SearchByNameOrNumber { get; set; }
        public long? MinBudget { get; set; }
        public long? MaxBudget { get; set; }
        public List<LeadFilterTypeMobile>? FilterTypes { get; set; }
        public BaseLeadVisibility LeadVisibility { get; set; }
        public List<Guid>? AssignToIds { get; set; }
        public List<Budget>? Budget { get; set; }
        public List<string>? Projects { get; set; }
        public List<string>? Properties { get; set; }
        public List<double>? NoOfBHKs { get; set; }
        public List<int>? Beds { get; set; }
        public List<int>? Baths { get; set; }
        public List<string>? Floors { get; set; }
        public List<OfferType>? OfferTypes { get; set; }
        public List<FurnishStatus>? Furnished { get; set; }
        public List<BHKType>? BHKTypes { get; set; }
        public List<Guid>? PropertyType { get; set; }
        public List<Guid>? PropertySubType { get; set; }
        public List<Guid>? StatusIds { get; set; }
        public List<Guid>? SubStatuses { get; set; }
        public List<Dtos.BudgetFilter>? BudgetFilters { get; set; }
        public List<string>? Locations { get; set; }
        public List<MeetingOrVisitCompletionStatus>? MeetingOrVisitStatuses { get; set; }
        public DateTime? ToDateForMeetingOrVisit { get; set; }
        public DateTime? FromDateForMeetingOrVisit { get; set; }
        public List<Guid>? AppointmentDoneByUserIds { get; set; }
        public bool? IsWithTeam { get; set; }
        public List<string>? SubSources { get; set; }
        public List<Guid>? IntegrationAccountIds { get; set; }
        public List<string>? AgencyNames { get; set; }
        public List<Guid>? LeadIds { get; set; }
        public List<string>? SerialNumbers { get; set; }
        public string? ReferralName { get; set; }
        public string? ReferralContactNo { get; set; }
        public string? ReferralEmail { get; set; }
        public double? CarpetArea { get; set; }
        public double? MinCarpetArea { get; set; }
        public double? MaxCarpetArea { get; set; }
        public Guid CarpetAreaUnitId { get; set; }
        public float? ConversionFactor { get; set; }
        public List<string>? Designations { get; set; }
        public string? Designation { get; set; }
        public bool? IsPicked { get; set; }
        public List<Guid>? SecondaryUsers { get; set; }
        public bool? IsDualOwnershipEnabled { get; set; }
        public List<Guid>? BookedByIds { get; set; }
        public string? DatesJsonFormattedString { get; set; }
        public List<string>? CustomFlags { get; set; }
        public string? Currency { get; set; }
        public DateTime? CallLogFromDate { get; set; }
        public DateTime? CallLogToDate { get; set; }
        public CallStatus? CallStatus { get; set; }
        public CallDirection? CallDirection { get; set; }
        public List<Guid>? CustomFilterIds { get; set; }
        public List<Guid>? HistoryAssignedToIds { get; set; }
        public List<Guid>? AssignFromIds { get; set; }
        public List<Guid>? SecondaryFromIds { get; set; }
        public List<Guid>? DoneBy { get; set; }
        public bool? IsWithHistory { get; set; }
        public List<Guid>? DesignationsId { get; set; }
        public bool? IsGenManagerWithTeam { get; set; }
        public List<Guid>? GeneralManagerIds { get; set; }
        public bool? CanAccessAllLeads { get; set; }
        public List<string>? Communities { get; set; }
        public List<string>? SubCommunities { get; set; }
        public List<string>? TowerNames { get; set; }
        public List<string>? Countries { get; set; }
        public List<string>? PostalCodes { get; set; }
        public List<string>? Cities { get; set; }
        public List<string>? States { get; set; }
        public List<string>? CampaignNames { get; set; }
        public bool? DataConverted { get; set; }
        public List<Guid>? QualifiedByIds { get; set; }
        public string? ConfidentialNotes { get; set; }
        public double? BuiltUpArea { get; set; }
        public double? MinBuiltUpArea { get; set; }
        public double? MaxBuiltUpArea { get; set; }
        public Guid BuiltUpAreaUnitId { get; set; }
        public double? SaleableArea { get; set; }
        public double? MinSaleableArea { get; set; }
        public double? MaxSaleableArea { get; set; }
        public Guid SaleableAreaUnitId { get; set; }
        public double? PropertyArea { get; set; }
        public double? MinPropertyArea { get; set; }
        public double? MaxPropertyArea { get; set; }
        public Guid PropertyAreaUnitId { get; set; }
        public double? NetArea { get; set; }
        public double? MinNetArea { get; set; }
        public double? MaxNetArea { get; set; }
        public Guid NetAreaUnitId { get; set; }
        public string? UnitName { get; set; }
        public List<string>? ClusterName { get; set; }
        public List<string>? Nationality { get; set; }
        public List<string>? UnitNames { get; set; }

        public List<Guid>? CreatedByIds { get; set; }
        public List<Guid>? LastModifiedByIds { get; set; }
        public List<Guid>? ArchivedByIds { get; set; }
        public List<Guid>? RestoredByIds { get; set; }
        public List<Guid>? ClosingManagers { get; set; }
        public List<Guid>? SourcingManagers { get; set; }
        public string? AdditionalPropertiesKey { get; set; }
        public string? AdditionalPropertiesValue { get; set; }
        public List<string>? ChannelPartnerNames { get; set; }
        public List<Profession>? Profession { get; set; }
        public string? UploadTypeName { get; set; }
        public List<Purpose>? Purposes { get; set; }
        public int? ChildLeadsCount { get; set; }
        public long? FromMinBudget { get; set; }
        public long? ToMinBudget { get; set; }
        public long? FromMaxBudget { get; set; }
        public long? ToMaxBudget { get; set; }
        public bool? IsUntouched { get; set; }
        public List<Guid>? OriginalOwnerIds { get; set; }
        public PossesionType? PossesionType { get; set; }
        public DateTime? FromPossesionDate { get; set; }
        public DateTime? ToPossesionDate { get; set; }
        public List<string>? LandLine { get; set; }
        public bool? ShowOnlyParentLeads { get; set; }
        public List<LeadType>? LeadType { get; set; }

        public List<string>? PropertyToSearch { get; set; }

        public List<string>? CountryCode { get; set; }
        public List<string>? AltCountryCode { get; set; }
        public List<Gender>? GenderTypes { get; set; }
        public List<MaritalStatusType>? MaritalStatuses { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public List<Guid>? UserIds { get; set; }
        public List<CallStatus>? CallStatuses { get; set; }
        public List<CallDirection>? CallDirections { get; set; }
        public OwnerSelectionType? OwnerSelection { get; set; }
        public string? TimeZoneId { get; set; }
        public TimeSpan? BaseUTcOffset { get; set; }

    }
    public class GetAllLeadsByCustomFiltersRequest : GetAllLeadsParametersNewFilters, IRequest<Response<V5GetAllLeadsWrapperDto>>
    {

    }
    public class GetAllLeadsByCustomFiltersRequestHandler : IRequestHandler<GetAllLeadsByCustomFiltersRequest, Response<V5GetAllLeadsWrapperDto>>
    {
        private readonly ICurrentUser _currentUser;
        private readonly ILeadRepository _efLeadRepository;
        private readonly IDapperRepository _dapperRepository;
        private readonly IRepositoryWithEvents<CustomFilter> _repo;
        private readonly IRepositoryWithEvents<Domain.Entities.UserDetails> _userDetails;
        public GetAllLeadsByCustomFiltersRequestHandler(
            ICurrentUser currentUser,
            ILeadRepository efLeadRepository,
            IDapperRepository dapperRepository,
            IRepositoryWithEvents<CustomFilter> repo,
            IRepositoryWithEvents<Domain.Entities.UserDetails> userDetails)
        {
            _currentUser = currentUser;
            _efLeadRepository = efLeadRepository;
            _dapperRepository = dapperRepository;
            _repo = repo;
            _userDetails = userDetails;
        }

        public async Task<Response<V5GetAllLeadsWrapperDto>> Handle(GetAllLeadsByCustomFiltersRequest request, CancellationToken cancellationToken)
        {
            var userId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            var subIds = new List<Guid>();
            (List<AppointmentType>, List<bool>) appointments = new();
            var getAllLeadsWrapperDto = new V5GetAllLeadsWrapperDto();
            List<CustomFilter>? filters = null;
            if (request.IsGenManagerWithTeam ?? false && (request.GeneralManagerIds?.Any() ?? false))
            {
                var generalManagerIds = await _dapperRepository.GeneralManagerAsync(request.GeneralManagerIds ?? new(), tenantId ?? string.Empty);
                if (generalManagerIds?.Any() ?? false)
                {
                    var subordinateIds = await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(generalManagerIds.ToList(), tenantId ?? string.Empty);
                    if (request.AssignToIds == null)
                    {
                        request.AssignToIds = new List<Guid>();
                    }
                    request.AssignToIds.AddRange(subordinateIds);
                    request.AssignToIds.AddRange(generalManagerIds);
                    request.AssignToIds.AddRange(request.GeneralManagerIds);
                }
                if (request.AssignTo == null)
                {
                    request.AssignToIds = new List<Guid>();
                }
                request.AssignToIds.AddRange(request.GeneralManagerIds);
            }
            else if ((request.GeneralManagerIds?.Any() ?? false))
            {
                if (request.AssignToIds == null)
                {
                    request.AssignToIds = new List<Guid>();
                }
                request.AssignToIds.AddRange(request.GeneralManagerIds);

            }
            if (request?.DesignationsId?.Any() ?? false)
            {
                var users = await _userDetails.ListAsync(new Lrb.Application.Dashboard.Web.Specs.GetUsersByDesignationIdSpec(request.DesignationsId));
                var userIds = users.Select(i => i.UserId).ToList();
                if (request.AssignToIds == null)
                {
                    request.AssignToIds = new List<Guid>();
                }
                request?.AssignToIds.AddRange(userIds);

            }
            var isAdmin = await _dapperRepository.IsAdminV2Async(userId, tenantId ?? string.Empty);
            var tasks = new Task[]
            {
               Task.Run(async () => request.IsDualOwnershipEnabled = request.IsDualOwnershipEnabled == null ? await _dapperRepository.V2GetDualOwnershipDetails(tenantId ?? string.Empty) : request.IsDualOwnershipEnabled),
               Task.Run(async () => subIds = await GetSubordinateIdsAsync(request.Adapt<GetAllLeadsParametersNewFilters>(), userId, tenantId ?? string.Empty,isAdmin)),
               Task.Run(async () => appointments = await GetAppointmentTypes(request.Adapt<GetAllLeadsParametersNewFilters>())),
               Task.Run(async () =>
               {
                   if (request.CustomFilterIds?.Any() ?? false)
                   {
                      filters = await _repo.ListAsync(new GetCustomFiltersSpec(request.CustomFilterIds ?? new(),userId), cancellationToken);
                       if(!filters?.Any() ?? false)
                       {
                            var res = (await _dapperRepository.QueryStoredProcedureAsync<ViewCustomFilterDto>("LeadratBlack", "CreateCustomFiltersCartViewForUser", 
                                new
                                {
                                  tenantid = tenantId,
                                  currentuser = userId,
                                   module = "leads"
                                })).ToList();
                       }
                       filters = await _repo.ListAsync(new GetCustomFiltersSpec(request.CustomFilterIds ?? new(),userId), cancellationToken);
                   }
                   else
                   {
                      filters = await _repo.ListAsync(new GetCustomFiltersSpec(userId, true, true, isAdmin), cancellationToken);
                       if(!filters?.Any() ?? false)
                       {
                            var res = (await _dapperRepository.QueryStoredProcedureAsync<ViewCustomFilterDto>("LeadratBlack", "CreateCustomFiltersCartViewForUser",
                                new
                                {
                                  tenantid = tenantId,
                                  currentuser = userId,
                                   module = "leads"
                                })).ToList();
                       }
                      filters = await _repo.ListAsync(new GetCustomFiltersSpec(userId, true, true, isAdmin), cancellationToken);
                   }
               }),
            };
            await Task.WhenAll(tasks);
            var totalCountTasks = new Task[]
            {
                Task.Run(async () => getAllLeadsWrapperDto.TotalLeadsCount = await _efLeadRepository.GetLeadsCountByCustomFiltersForMobileAsync(null, request.Adapt<GetAllLeadsParametersNewFilters>(), subIds, userId, isAdmin, new List<Guid>())),
            };
            if (filters?.Any() ?? false)
            {
                await GetLeadCategoryDtosAsync(getAllLeadsWrapperDto, request, userId, subIds, isAdmin, appointments, filters);
            }
            await Task.WhenAll(totalCountTasks);
            return new Response<V5GetAllLeadsWrapperDto>(getAllLeadsWrapperDto);
        }
        private async Task GetLeadCategoryDtosAsync(V5GetAllLeadsWrapperDto getAllLeadsWrapperDto, GetAllLeadsParametersNewFilters request, Guid userId, List<Guid> subIds, bool isAdmin, (List<AppointmentType>, List<bool>) appointments, IEnumerable<CustomFilter> filters)
        {
            var response = new ConcurrentDictionary<string, V5LeadCategoryDto>();
            await Task.WhenAll(filters.Select(async filter =>
            {
                var leads = (await _efLeadRepository.GetAllLeadsByCustomFiltersForMobileAsync(filter ?? new CustomFilter(), request.Adapt<GetAllLeadsParametersNewFilters>(), subIds, userId, isAdmin, new List<Guid>())).ToList();
                var count = await _efLeadRepository.GetLeadsCountByCustomFiltersForMobileAsync(filter ?? new CustomFilter(), request.Adapt<GetAllLeadsParametersNewFilters>(), subIds, userId, isAdmin, new List<Guid>());
                if (leads?.Any() ?? false)
                {
                    leads.ToList().ForEach(lead => FilterAppointments(request, isAdmin, userId, lead, appointments.Item1 ?? new(), appointments.Item2 ?? new(), subIds));
                    var result = new V5LeadCategoryDto
                    {
                        LeadFilter = filter?.Name ?? string.Empty,
                        OrderRank = filter?.OrderRank ?? 0,
                        FilterId = filter?.Id ?? Guid.Empty,
                        Leads = leads.Adapt<List<V5GetAllLeadDto>>(),
                        TotalCount = count
                    };
                    response.TryAdd(filter?.Name ?? string.Empty, result);
                }
            }));
            var sortedResponse = response.OrderBy(kvp => kvp.Value.OrderRank)
                         .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
            getAllLeadsWrapperDto.Leads = sortedResponse;
        }
        private async Task<List<Guid>> GetSubordinateIdsAsync(GetAllLeadsParametersNewFilters request, Guid userId, string tenantId, bool isAdmin)
        {


            var assignToIds = request?.AssignToIds ?? new List<Guid>();
            return assignToIds.Any()
            ? (request?.IsWithTeam ?? false)
                    ? (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesV2Async(assignToIds, tenantId ?? string.Empty)).ToList()
            : assignToIds
                : (await _dapperRepository.GetSubordinateIdsV2Async(userId, tenantId ?? string.Empty, request?.CanAccessAllLeads, isAdmin))?.ToList() ?? new List<Guid>();
        }
        public async Task<(List<AppointmentType>, List<bool>)> GetAppointmentTypes(GetAllLeadsParametersNewFilters request)
        {
            List<AppointmentType> appTypes = new();
            List<bool> appDoneStatuses = new();
            if (request.MeetingOrVisitStatuses == null)
            {
                return (appTypes, appDoneStatuses);
            }
            request.MeetingOrVisitStatuses?.ForEach(appType =>
            {
                switch (appType)
                {
                    case MeetingOrVisitCompletionStatus.IsMeetingDone:
                        appTypes.Add(AppointmentType.Meeting);
                        appDoneStatuses.Add(true);
                        break;
                    case MeetingOrVisitCompletionStatus.IsMeetingNotDone:
                        appTypes.Add(AppointmentType.Meeting);
                        appDoneStatuses.Add(false);
                        break;
                    case MeetingOrVisitCompletionStatus.IsSiteVisitDone:
                        appTypes.Add(AppointmentType.SiteVisit);
                        appDoneStatuses.Add(true);
                        break;
                    case MeetingOrVisitCompletionStatus.IsSiteVisitNotDone:
                        appTypes.Add(AppointmentType.SiteVisit);
                        appDoneStatuses.Add(false);
                        break;
                }
            });
            appTypes = appTypes.Distinct().ToList();
            appDoneStatuses = appDoneStatuses.Distinct().ToList();
            return (appTypes, appDoneStatuses);
        }
        private void FilterAppointments(GetAllLeadsParametersNewFilters request, bool isAdmin, Guid userId, Domain.Entities.Lead lead, List<AppointmentType> appTypes, List<bool> appDoneStatuses, List<Guid> subIds)
        {
            if (lead?.Appointments?.Any() ?? false)
            {
                if (request.AppointmentDoneByUserIds?.Any() ?? false)
                {
                    var uniqueAppointments = lead.Appointments.Where(i => i.UniqueKey != null && i.UniqueKey != Guid.Empty).DistinctBy(i => i.UniqueKey).ToList();
                    uniqueAppointments.AddRange(lead.Appointments.Where(i => i.UniqueKey == null || i.UniqueKey == Guid.Empty).ToList());
                    lead.Appointments = uniqueAppointments.Where(i => request.AppointmentDoneByUserIds.Contains(i.CreatedBy) && appTypes.Contains(i.Type) && appDoneStatuses.Contains(i.IsDone)).ToList();
                }
                else if (subIds != null && !(isAdmin))
                {
                    if ((lead?.Appointments?.Any() ?? false) && lead.Appointments.Any(i => subIds.Contains(i.UserId)))
                    {
                        var appointmentsWithoutUniqueKey = lead.Appointments?.Where(i => i.UniqueKey == null || i.UniqueKey == default).ToList() ?? new();

                        var appointmentsWithUniqueKey = lead.Appointments?.Where(i => i.UniqueKey != null && i.UniqueKey != default && subIds.Contains(i.UserId))?.DistinctBy(i => i.UniqueKey)?.OrderBy(i => i.LastModifiedOn)?.ToList() ?? new();

                        appointmentsWithoutUniqueKey.AddRange(appointmentsWithUniqueKey);
                        lead.Appointments = appointmentsWithoutUniqueKey;
                    }
                    else
                    {
                        lead.Appointments = null;
                    }
                }
                else
                {
                    var appointmentsWithoutUniqueKey = lead.Appointments?.Where(i => i.UniqueKey == null || i.UniqueKey == default)?.ToList() ?? new();
                    var appointmentsWithUniqueKey = lead.Appointments?.Where(i => i.UniqueKey != null && i.UniqueKey != default)?.DistinctBy(i => i.UniqueKey).ToList() ?? new();
                    appointmentsWithoutUniqueKey.AddRange(appointmentsWithUniqueKey);
                    lead.Appointments = appointmentsWithoutUniqueKey;
                }
            }
        }
    }
}
