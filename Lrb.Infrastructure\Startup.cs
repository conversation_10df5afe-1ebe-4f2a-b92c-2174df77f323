﻿using Hangfire;
using Lrb.Application.Common.Services;
using Lrb.Infrastructure.ApiClientService;
using Lrb.Infrastructure.ApiResposeTimeInfo;
using Lrb.Infrastructure.Auth;
using Lrb.Infrastructure.AWS_Batch;
using Lrb.Infrastructure.BackgroundJobs;
using Lrb.Infrastructure.BlobStorage;
using Lrb.Infrastructure.Caching;
using Lrb.Infrastructure.Common;
using Lrb.Infrastructure.Cors;
using Lrb.Infrastructure.DomainSettings;
using Lrb.Infrastructure.DynamoDb;
using Lrb.Infrastructure.Extensions;
using Lrb.Infrastructure.Facebook;
using Lrb.Infrastructure.FileStorage;
using Lrb.Infrastructure.GlobalSearch;
using Lrb.Infrastructure.Gmail;
using Lrb.Infrastructure.GoogleAds;
using Lrb.Infrastructure.GoogleAuth;
using Lrb.Infrastructure.GooglePlaces;
using Lrb.Infrastructure.GoogleSheets;
using Lrb.Infrastructure.GraphEmail;
using Lrb.Infrastructure.IVR;
using Lrb.Infrastructure.Knowlarity;
using Lrb.Infrastructure.Lambda;
using Lrb.Infrastructure.LeadRotation;
using Lrb.Infrastructure.Listing;
using Lrb.Infrastructure.Mailing;
using Lrb.Infrastructure.Mapping;
using Lrb.Infrastructure.Middleware;
using Lrb.Infrastructure.Multitenancy;
using Lrb.Infrastructure.OpenApi;
using Lrb.Infrastructure.Persistence;
using Lrb.Infrastructure.Persistence.Initialization;
using Lrb.Infrastructure.PushNotification;
using Lrb.Infrastructure.Queue;
using Lrb.Infrastructure.SecretsManager;
using Lrb.Infrastructure.SecurityHeaders;
using Lrb.Infrastructure.ServiceBus;
using Lrb.Infrastructure.Sms;
using Lrb.Infrastructure.WhatsApp;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Reflection;

namespace Lrb.Infrastructure;

public static class Startup
{
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration config)
    {

        return services
            //Add this middleware (AddExceptionMiddleware) at First position, on top of all services.
            .AddExceptionMiddleware()
            .AddHttpContextAccessor()
            .AddApiVersioning()
            .AddAuth(config)
            .AddCaching(config)
            .AddCorsPolicy(config)
            .AddCognitoJwtMiddleware()
            .AddHealthCheck()
            .AddMailing(config)
            .AddMediatR(Assembly.GetExecutingAssembly())
            .AddMultitenancy(config)
            .AddFileStorage(config)
            .AddOpenApiDocumentation(config)
            .AddPersistence(config)
            .AddRequestLogging(config)
            .AddRouting(options => options.LowercaseUrls = true)
            .AddServices()
            .AddGooglePlaces(config)
            .AddBlobStorage(config)
            .AddGoogleAuth(config)
            .AddGmail(config)
            .AddKnowlarity(config)
            .AddGlobalSearch(config)
            .AddServetel(config)
            .AddGlobalSearch(config)
            .AddFacebook(config)
            .AddNotification(config)
            .AddGraphEmail(config)
            .AddDomain(config)
            .AddSms(config)
            .AddBackgroundJobs(config)
            .AddAWSBatch(config)
            .AddAzureServiceBus(config)
            .AddSecretsManager()
            .AddGoogleSheets(config)
            .AddDynamoDb(config)
            .AddWhatsApp(config)
            .AddGoogleAds(config)
            .AddLeadRotation(config)
             .AddApiClientConfiguration(config)
             .AddQueueService(config)
             .AddListing(config)
            .ConfigureServices();

    }
    public static IServiceCollection AddInfrastructureForWeb(this IServiceCollection services, IConfiguration config)
    {

        return services
            //Add this middleware (AddExceptionMiddleware) at First position, on top of all services.
            .AddExceptionMiddleware()
            .AddHttpContextAccessor()
            .AddApiVersioning()
            .AddAuthForWeb(config)
            .AddCaching(config)
            .AddCorsPolicy(config)
            .AddCognitoJwtMiddleware()
            .AddHealthCheck()
            .AddMailing(config)
            .AddMediatR(Assembly.GetExecutingAssembly())
            .AddMultitenancy(config)
            .AddFileStorage(config)
            .AddOpenApiDocumentation(config)
            .AddPersistence(config)
            .AddRequestLogging(config)
            .AddRouting(options => options.LowercaseUrls = true)
            .AddServices()
            .AddGooglePlaces(config)
            .AddBlobStorage(config)
            .AddGoogleAuth(config)
            .AddGmail(config)
            .AddGlobalSearch(config)
            .AddServetel(config)
            .AddGlobalSearch(config)
            .AddFacebook(config)
            .AddNotification(config)
            .AddGraphEmail(config)
            .AddDomain(config)
            .AddSms(config)
            .AddBackgroundJobs(config)
            .AddDynamoDb(config)
            .AddLambda(config)
            .AddAWSBatch(config)
            .AddAzureServiceBus(config)
            .AddSecretsManager()
            .AddGoogleSheets(config)
            .AddWhatsApp(config)
            .AddLeadRotation(config)
            .AddGoogleAds(config)
            .AddApiClientConfiguration(config)
            .AddQueueService(config)
            .AddListing(config)
            .ConfigureServices();


    
    }
    public static IServiceCollection AddInfrastructureForMobile(this IServiceCollection services, IConfiguration config)
    {

        return services
            //Add this middleware (AddExceptionMiddleware) at First position, on top of all services.
            .AddExceptionMiddleware()
            .AddHttpContextAccessor()
            .AddApiVersioning()
            .AddAuthForMobile(config)
            .AddCaching(config)
            .AddCorsPolicy(config)
            .AddCognitoJwtMiddleware()
            .AddHealthCheck()
            .AddMailing(config)
            .AddMediatR(Assembly.GetExecutingAssembly())
            .AddMultitenancy(config)
            .AddFileStorage(config)
            //.AddNotifications(config)
            .AddOpenApiDocumentation(config)
            .AddPersistence(config)
            .AddRequestLogging(config)
            .AddRouting(options => options.LowercaseUrls = true)
            .AddServices()
            .AddGooglePlaces(config)
            .AddBlobStorage(config)
            .AddGoogleAuth(config)
            .AddGmail(config)
            .AddGlobalSearch(config)
            .AddServetel(config)
            .AddGlobalSearch(config)
            .AddFacebook(config)
            .AddNotification(config)
            .AddGraphEmail(config)
            .AddDomain(config)
            .AddSms(config)
            .AddBackgroundJobs(config)
            .AddDynamoDb(config)
            .AddLambda(config)
            .AddAWSBatch(config)
            .AddAzureServiceBus(config)
            .AddSecretsManager()
            .AddGoogleSheets(config)
            .AddWhatsApp(config)
            .AddGoogleAds(config)
            .AddLeadRotation(config)
            .AddApiClientConfiguration(config)
            .AddQueueService(config)
            .AddListing(config)
            .ConfigureServices();

    }
    public static IServiceCollection AddInfrastructureForLambda(this IServiceCollection services, IConfiguration config, string tenantId = "root")
    {
        return services
            //Add this middleware (AddExceptionMiddleware) at First position, on top of all services.
            .AddExceptionMiddleware()
            .AddHttpContextAccessor()
            .AddApiVersioning()
            .AddAuthForMobile(config)
            .AddCaching(config)
            .AddCorsPolicy(config)
            .AddCognitoJwtMiddleware()
            .AddHealthCheck()
            .AddMailing(config)
            .AddMediatR(Assembly.GetExecutingAssembly())
            .AddLambdaMultitenancy(config, tenantId)
            .AddFileStorage(config)
            //.AddNotifications(config)
            .AddOpenApiDocumentation(config)
            .AddPersistence(config)
            .AddRequestLogging(config)
            .AddRouting(options => options.LowercaseUrls = true)
            .AddServices()
            .AddGooglePlaces(config)
            .AddBlobStorage(config)
            .AddGoogleAuth(config)
            .AddGmail(config)
            .AddGlobalSearch(config)
            .AddServetel(config)
            .AddGlobalSearch(config)
            .AddFacebook(config)
            .AddNotification(config)
            .AddGraphEmail(config)
            .AddDomain(config)
            .AddSms(config)
            .AddBackgroundJobs(config)
            .AddDynamoDb(config)
            .AddLambda(config)
            .AddAWSBatch(config)
            .AddAzureServiceBus(config)
            .AddSecretsManager()
            .AddGoogleSheets(config)
            .AddWhatsApp(config)
            .AddLeadRotation(config)
            .AddGoogleAds(config)
            .AddApiClientConfiguration(config)
            .AddQueueService(config)
            .AddListing(config)
            .ConfigureServices();
    }

    private static IServiceCollection AddApiVersioning(this IServiceCollection services) =>
        services.AddApiVersioning(config =>
        {
            config.DefaultApiVersion = new ApiVersion(1, 0);
            config.AssumeDefaultVersionWhenUnspecified = true;
            config.ReportApiVersions = true;
        });

    private static IServiceCollection AddHealthCheck(this IServiceCollection services) =>
        services.AddHealthChecks().AddCheck<TenantHealthCheck>("Tenant").Services;

    public static async Task InitializeDatabasesAsync(this IServiceProvider services, CancellationToken cancellationToken = default)
    {
        // Create a new scope to retrieve scoped services
        using var scope = services.CreateScope();

        await scope.ServiceProvider.GetRequiredService<IDatabaseInitializer>()
            .InitializeDatabasesAsync(cancellationToken);
    }
    public static void ConfigureMappings(this IServiceProvider services)
    {
        using var scope = services.CreateScope();
        MapsterSettings.Configure(scope.ServiceProvider);
    }
    public static void Conig(this IServiceProvider services)
    {
        using var scope = services.CreateScope();
        ServiceLocator.Configure(services);
    }
    public static IApplicationBuilder UseInfrastructure(this IApplicationBuilder builder, IConfiguration config) =>
        builder
            //Use this middleware (AddExceptionMiddleware) at First position, on top of all services.
            .UseExceptionMiddleware()
            .UseCognitoJwtMiddleware()
            .UseRequestLocalization()
            .UseStaticFiles()
            .UseSecurityHeaders(config)
            //.UseFileStorage()
            .UseRouting()
            .UseCorsPolicy()
            .UseAuthentication()
            .UseMultiTenancy()
            .UseCurrentUser()
            .UseAuthorization()
            .UseRequestLogging(config)
            .UseHangfireDashboard(config)
            .UseOpenApiDocumentation(config)
            .UseCustomSecurityHeaders();
    public static IEndpointRouteBuilder MapEndpoints(this IEndpointRouteBuilder builder)
    {
        builder.MapControllers().RequireAuthorization();

        #region Health Check

        // Map / to check it is alive or not
        builder.MapGet("/", context =>
        {
            // Perform health checks and return appropriate status
            return context.Response.WriteAsync("API is live!");
        }).AllowAnonymous();

        // Map /api/health to HealthCheck
        builder.MapHealthCheck();

        // Map /health-check to HealthCheck
        builder.MapGet("/health-check", context =>
        {
            // Perform health checks and return appropriate status
            return context.Response.WriteAsync("API is healthy!");
        }).AllowAnonymous();

        // Map /admin/host/ping
        builder.MapGet("/admin/host/ping", async context =>
        {
            // Perform admin/host/ping logic and return appropriate response
            await context.Response.WriteAsync("Ping successful!");
        }).AllowAnonymous();

        builder.MapPost("/admin/host/ping", async context =>
        {
            // Perform admin/host/ping logic and return appropriate response
            await context.Response.WriteAsync("Ping successful!");
        }).AllowAnonymous();
        #endregion
        //builder.MapHangfireDashboardAnonymousEndpoint();
        //builder.MapNotifications();
        return builder;
    }

    private static IEndpointConventionBuilder MapHealthCheck(this IEndpointRouteBuilder endpoints) =>
        endpoints.MapHealthChecks("/api/health").AllowAnonymous();

    private static IEndpointConventionBuilder MapHangfireDashboardAnonymousEndpoint(this IEndpointRouteBuilder endpoints) =>
        endpoints.MapHangfireDashboard("/jobs", new DashboardOptions()
        {
            Authorization = Enumerable.Empty<Hangfire.Dashboard.IDashboardAuthorizationFilter>()
        }).AllowAnonymous();

}
