﻿using Lrb.Application;
using Lrb.Application.Agency.Web;
using Lrb.Application.Agency.Web.Requests;
using Lrb.Application.Campaigns.Request;
using Lrb.Application.Campaigns.web.Request;
using Lrb.Application.ChannelPartner.Web.Dtos;
using Lrb.Application.ChannelPartner.Web.Request;
using Lrb.Application.Marketing.Web.Dtos;
using Lrb.Application.Marketing.Web.Requests;
using Lrb.Domain.Entities.Marketing;

namespace Lrb.WebApi.Host.Controllers
{
    [Authorize]
    public class MarketingController : VersionedApiController
    {
        #region Agency
        [HttpGet("agencies")]
        [TenantIdHeader]
        [OpenApiOperation("Agencies using available filters.", "")]
        public async Task<PagedResponse<ViewAgencyDto, string>> GetAsync([FromQuery] GetAgenciesRequest request)
        {
            var response = await Mediator.Send(request);
            return response;
        }
        [HttpPut("agency")]
        [TenantIdHeader]
        [OpenApiOperation("Update Agency.", "")]
        public async Task<Response<ViewAgencyDto>> UpdateAsync(UpdateAgencyRequest request)
        {
            var response = await Mediator.Send(request);
            return response;
        }
        [HttpPost("agency")]
        [TenantIdHeader]
        [OpenApiOperation("Create Agency.", "")]
        public async Task<Response<Guid>> CreateAsync(CreateAgencyRequest request)
        {
            var response = await Mediator.Send(request);
            return response;
        }
        [HttpDelete("agency")]
        [TenantIdHeader]
        [OpenApiOperation("Delete agency By Id.", "")]
        public async Task<Response<bool>> DeleteAgencyById(DeleteAgencyRequest request)
        {
            var response = await Mediator.Send(request);
            return response;
        }
        [HttpPost("excel")]
        [TenantIdHeader]
        [OpenApiOperation("Upload excel File")]
        public async Task<ActionResult<Response<Application.Lead.Web.FileColumnDto>>> UploadExcelFileAsync(IFormFile file)
        {

            return await Mediator.Send(new GetMarketingExcelColumnsUsingEPPlusRequest(file));
        }
        [HttpPost("agency/batch")]
        [TenantIdHeader]
        [OpenApiOperation("Create new Agencies by excel.", "")]
        public Task<Response<BulkMarketingAgencyUploadTracker>> UploadBulkAgencyAsync(RunAWSBatchForBulkMarketingAgencyRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpGet("agency/bulk/trackers")]
        [TenantIdHeader]
        [OpenApiOperation("Get all bulk agency trackers", "")]
        public async Task<PagedResponse<BulkMarketingAgencyUploadTracker, string>> GetAllTrackers([FromQuery] GetAllBulkAgencyTrackersRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("agency/export/email")]
        [TenantIdHeader]
        [OpenApiOperation("Export Marketing Agency by email", "")]
        public async Task<Response<Guid>> ExportAgencyAsync(RunAWSBatchForMarketingAgencyRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("agency/export/trackers")]
        [TenantIdHeader]
        [OpenApiOperation("Get all Export Marketig Agency Trackers")]
        public async Task<PagedResponse<ExportMarketingTrackerDto, string>> GetExportAgencyTrackers([FromQuery] GetAllExportAgencyTrackers request)
        {
            return await Mediator.Send(request);
        }

        #endregion
        #region ChannelPartner
        [HttpGet("channelpartners")]
        [TenantIdHeader]
        [OpenApiOperation("ChannelPartners using available filters.", "")]
        public async Task<PagedResponse<ViewChannelPartnerDto, string>> GetChannelPartnersAsync([FromQuery] GetChannelPartnersRequest request)
        {
            var response = await Mediator.Send(request);
            return response;
        }
        [HttpPut("channelpartner")]
        [TenantIdHeader]
        [OpenApiOperation("Update ChannelPartners.", "")]
        public async Task<Response<ViewChannelPartnerDto>> UpdateChannelPartnersAsync(UpdateChannelPartnerRequest request)
        {
            var response = await Mediator.Send(request);
            return response;
        }
        [HttpPost("channelpartner")]
        [TenantIdHeader]
        [OpenApiOperation("Create ChannelPartner.", "")]
        public async Task<Response<Guid>> CreateChannelPartnersAsync(CreateChannelPartnerRequest request)
        {
            var response = await Mediator.Send(request);
            return response;
        }

        [HttpDelete("channelpartner")]
        [TenantIdHeader]
        [OpenApiOperation("Delete ChannelPartner By Id.", "")]
        public async Task<Response<bool>> DeleteChannelPartnerById(DeleteChannelPartnerRequest request)
        {
            var response = await Mediator.Send(request);
            return response;
        }
        [HttpPost("channelpartners/batch")]
        [TenantIdHeader]
        [OpenApiOperation("Create ChannelPartners by excel.", "")]
        public Task<Response<BulkMarketingAgencyUploadTracker>> UploadBulkChannelPartnersAsync(RunAWSBatchForBulkMarketingCpRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpGet("channelpartners/bulk/trackers")]
        [TenantIdHeader]
        [OpenApiOperation("Get all bulk channel partners trackers", "")]
        public async Task<PagedResponse<BulkMarketingAgencyUploadTracker, string>> GetAllCpTrackers([FromQuery] GetAllBulkCpTrackersRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("channelpartners/export/email")]
        [TenantIdHeader]
        [OpenApiOperation("Export Marketing ChannelPartner by email", "")]
        public async Task<Response<Guid>> ExportCpAsync(RunAWSBatchForMarketingChannelPartnerRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("channelpartners/export/Trackers")]
        [TenantIdHeader]
        [OpenApiOperation("Get all Export Marketig Cp Trackers")]
        public async Task<PagedResponse<ExportMarketingTrackerDto, string>> GetAllTrackers([FromQuery] GetAllExportCpTracker request)
        {
            return await Mediator.Send(request);
        }
        #endregion

        [HttpGet("agency/names")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.ViewForFilter, LrbResource.Users)]
        [OpenApiOperation("Get all AgencyNames.", "")]
        public async Task<ActionResult<Response<bool>>> GetAgencyNamesAsync([FromQuery] string agency)
        {
            var response = await Mediator.Send(new GetAgencyByNameRequest(agency));
            return Ok(response);
        }

        [HttpGet("channelpartner/names")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.ViewForFilter, LrbResource.Users)]
        [OpenApiOperation("Get all ChannelPartnerNames.", "")]
        public async Task<ActionResult<Response<bool>>> GetChannelPartnerNamesAsync([FromQuery] string channelPartner)
        {
            var response = await Mediator.Send(new GetChannelPartnerByNameRequest(channelPartner));
            return Ok(response);
        }

        [HttpGet("agency/addresses")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all agency address.", "")]
        public async Task<Response<List<string>>> GetAgencyAddressesAsync()
        {
            return await Mediator.Send(new GetAllAgencyAddressRequest());
        }

        [HttpGet("channelpartner/addresses")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get all ChannelPartner address.", "")]
        public async Task<Response<List<string>>> GetChannelPartnerAddressesAsync()
        {
            return await Mediator.Send(new GetAllChannelPartnersAddressRequest());
        }
        #region Campaigns
        [HttpGet("Campaigns")]
        [TenantIdHeader]
        [OpenApiOperation("Campaigns using available filters.", "")]
        public async Task<PagedResponse<ViewCampaignDto, string>> GetCampaignsAsync([FromQuery] GetAllCampaingsRequest request)
        {
            var response = await Mediator.Send(request);
            return response;
        }
        [HttpPut("Campaigns")]
        [TenantIdHeader]
        [OpenApiOperation("Update Campaigns.", "")]
        public async Task<Response<ViewCampaignDto>> UpdateCampaignsAsync(UpdateCampaignsRequest request)
        {
            var response = await Mediator.Send(request);
            return response;
        }
        [HttpPost("Campaigns")]
        [TenantIdHeader]
        [OpenApiOperation("Create Campaigns.", "")]
        public async Task<Response<Guid>> CreateCamapignsAsync(CreateCampaignsRequest request)
        {
            var response = await Mediator.Send(request);
            return response;
        }

        [HttpDelete("Campaigns")]
        [TenantIdHeader]
        [OpenApiOperation("Delete Campaigns By Id.", "")]
        public async Task<Response<bool>> DeleteCampaignsById(DeleteCampaignbyIdRequest request)
        {
            var response = await Mediator.Send(request);
            return response;
        }
        [HttpPost("campaign/batch")]
        [TenantIdHeader]
        [OpenApiOperation("Create new Camapign by excel.", "")]
        public Task<Response<BulkMarketingAgencyUploadTracker>> UploadBulkCampaignAsync(RunAWSBatchForBulkMarketingCampaignRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpGet("campaign/bulk/trackers")]
        [TenantIdHeader]
        [OpenApiOperation("Get all bulk campaign trackers", "")]
        public async Task<PagedResponse<BulkMarketingAgencyUploadTracker, string>> GetAllCamapignTrackers([FromQuery] GetAllBulkCamapignTrackersRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("Campaign/export/email")]
        [TenantIdHeader]
        [OpenApiOperation("Export Marketing Campaign by email", "")]
        public async Task<Response<Guid>> ExportCampaignAsync(RunAWSBatchForMarketingCampaignRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("Campaign/export/trackers")]
        [TenantIdHeader]
        [OpenApiOperation("Get all Export Marketig Campaign Trackers")]
        public async Task<PagedResponse<ExportMarketingTrackerDto, string>> GetExportCamapignTrackers([FromQuery] GetAllExportCampaignTrackers request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("Camapaign/names")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.ViewForFilter, LrbResource.Users)]
        [OpenApiOperation("Get all CamapignNames.", "")]
        public async Task<ActionResult<Response<bool>>> GetCamapignNamesAsync([FromQuery] string campaign)
        {
            var response = await Mediator.Send(new GetCampaignByNameRequest(campaign));
            return Ok(response);
        }
        #endregion
    }
}