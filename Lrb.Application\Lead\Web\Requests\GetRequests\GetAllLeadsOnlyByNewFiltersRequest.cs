﻿using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Property.Web;
using Lrb.Application.Utils;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Microsoft.Graph;
using Newtonsoft.Json;

namespace Lrb.Application.Lead.Web
{
    public class GetAllLeadsParametersNewFilters : PaginationFilter
    {

        public BaseLeadVisibility LeadVisibility { get; set; }
        public List<LeadTagEnum>? LeadTags { get; set; }
        public List<EnquiryType>? EnquiredFor { get; set; }
        public DateType? DateType { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public List<Guid>? AssignTo { get; set; }
        public List<Guid>? OriginalOwner { get; set; }
        public List<LeadSource>? Source { get; set; } = new();
        public List<Budget>? Budget { get; set; }
        public List<string>? Projects { get; set; }
        public List<string>? Properties { get; set; }
        public List<double>? NoOfBHKs { get; set; }
        public List<BHKType>? BHKTypes { get; set; }
        public List<Guid>? PropertyType { get; set; }
        public List<Guid>? PropertySubType { get; set; }
        public List<Guid>? StatusIds { get; set; }
        public string? SearchByNameOrNumber { get; set; }
        public List<string>? Locations { get; set; }
        public List<string>? Localities { get; set; }
        public List<MeetingOrVisitCompletionStatus>? MeetingOrVisitStatuses { get; set; }
        public long? MinBudget { get; set; }
        public long? FromMinBudget { get; set; }
        public long? ToMinBudget { get; set; }
        public long? MaxBudget { get; set; }
        public long? FromMaxBudget { get; set; }
        public long? ToMaxBudget { get; set; }
        public bool? IsWithTeam { get; set; }
        public DateTime? ToDateForMeetingOrVisit { get; set; }
        public DateTime? FromDateForMeetingOrVisit { get; set; }
        public List<Guid>? AppointmentDoneByUserIds { get; set; }
        public List<string>? SubSources { get; set; }
        public List<Guid>? IntegrationAccountIds { get; set; }
        public List<string>? AgencyNames { get; set; }
        public LeadTagFilterDto? TagFilterDto { get; set; }
        public FirstLevelFilter FirstLevelFilter { get; set; }
        public SecondLevelFilter SecondLevelFilter { get; set; }
        public ScheduledDateTypeFilter ScheduledDateTypeFilter { get; set; }
        public ScheduledType ScheduledType { get; set; }
        public List<Guid>? CreatedByIds { get; set; }
        public List<Guid>? LastModifiedByIds { get; set; }
        public List<Guid>? AssignedFromIds { get; set; }
        public List<Guid>? ArchivedByIds { get; set; }
        public List<string>? CompanyNames { get; set; }
        public string? CompanyName { get; set; }
        public List<Guid>? RestoredByIds { get; set; }
        public double? CarpetArea { get; set; }
        public double? MinCarpetArea { get; set; }
        public double? MaxCarpetArea { get; set; }
        public Guid CarpetAreaUnitId { get; set; }
        public float? ConversionFactor { get; set; }
        public List<Guid>? SubStatusIds { get; set; }
        public List<Guid>? LeadIds { get; set; }
        public List<string>? SerialNumbers { get; set; }
        public string? ReferralName { get; set; }
        public string? ReferralContactNo { get; set; }
        public string? ReferralEmail { get; set; }
        public List<Guid>? ClosingManagers { get; set; }
        public List<Guid>? SourcingManagers { get; set; }
        public List<Profession>? Profession { get; set; }
        public List<string>? Zones { get; set; }
        public List<string>? Cities { get; set; }
        public List<string>? States { get; set; }
        public Guid? SecondLevelFilterId { get; set; }
        public bool? IsOnlyReportees { get; set; }
        public List<string>? Designations { get; set; }
        public string? Designation { get; set; }
        public bool? IsPicked { get; set; }
        public List<Guid>? BookedByIds { get; set; }
        //  public DateTime? BookedDate { get; set; }
        //public UserType? UserType { get; set; }
        public List<Guid>? SecondaryUsers { get; set; }
        public SortingDto? SortingCriteria { get; set; }
        public LeadFilterTypeWeb FilterType { get; set; }
        public bool? IsDualOwnershipEnabled { get; set; }
        public List<Date>? Dates { get; set; }
        public string? DatesJsonFormattedString { get; set; }
        public List<Guid>? DuplicateLeadIds { get; set; }
        public bool? ShouldShowParentLead { get; set; }
        public DateTime? BookedDate { get; set; }
        public Guid? LeadBrokerageInfoId { get; set; }
        public List<Guid>? SecondaryIds { get; set; }
        public Guid? TeamHead { get; set; }
        public int? UpperAgreementLimit { get; set; }
        public int? LowerAgreementLimit { get; set; }
        public TokenType? PaymentMode { get; set; }
        public int? UpperDiscountLimit { get; set; }
        public int? LowerDiscountLimit { get; set; }
        public DiscountType? DiscountMode { get; set; }
        public bool? ShouldShowBookedDetails { get; set; }
        public bool? ShouldShowBrokerageInfo { get; set; }
        public Guid? CustomFilterId { get; set; }
        public List<string>? CustomFlags { get; set; }

        public string? Currency { get; set; }
        public DateTime? CallLogFromDate { get; set; }
        public DateTime? CallLogToDate { get; set; }
        public CallStatus? CallStatus { get; set; }
        public Lrb.Domain.Enums.CallDirection? CallDirection { get; set; }
        public string? BookedUnderName { get; set; }
        public double? TotalBrokerage { get; set; }
        public double? SoldPrice { get; set; }
        public double? BrokerageCharges { get; set; }
        public double? NetBrokerageAmount { get; set; }
        public double? GST { get; set; }
        public string? ReferralNumber { get; set; }
        public Guid? ReferredBy { get; set; }
        public double? Commission { get; set; }
        public string? CommissionUnit { get; set; }
        public double? EarnedBrokerage { get; set; }
        public BrokerageType? BrokerageType { get; set; }
        public string? GSTUnit { get; set; }
        public string? BrokerageUnit { get; set; }
        public string? DiscountUnit { get; set; }
        public double? UpperRemainingAmountLimit { get; set; }
        public double? LowerRemainingAmountLimit { get; set; }
        public PaymentType? PaymentType { get; set; }
        public double? CarParkingCharges { get; set; }
        public double? RemainingAmount { get; set; }
        public double? AdditionalCharges { get; set; }
        public string? IsUntouched { get; set; }
        public List<Guid>? CustomFilterBaseIds { get; set; }
        public string? Longitude { get; set; }
        public string? Latitude { get; set; }
        public double? RadiusInKm { get; set; }
        public string? UploadTypeName { get; set; }
        public List<Guid>? HistoryAssignedToIds { get; set; }
        public List<Guid>? SecondaryFromIds { get; set; }
        public List<Guid>? DoneBy { get; set; }
        public bool? IsWithHistory { get; set; }
        public List<Guid>? DesignationsId { get; set; }
        public List<Guid>? GeneralManagerIds { get; set; }
        public bool? IsGenManagerWithTeam { get; set; }
        public List<string>? ChannelPartnerNames { get; set; }
        public string? AdditionalPropertiesKey { get; set; }
        public string? AdditionalPropertiesValue { get; set; }
        public bool? CanAccessAllLeads { get; set; }
        public List<int>? Beds { get; set; }
        public List<int>? Baths { get; set; }
        public List<string>? Floors { get; set; }
        public List<OfferType>? OfferTypes { get; set; }
        public List<FurnishStatus>? Furnished { get; set; }
        public List<string>? Communities { get; set; }
        public List<string>? SubCommunities { get; set; }
        public List<string>? TowerNames { get; set; }
        public List<string>? Countries { get; set; }
        public List<string>? PostalCodes { get; set; }
        public double? BuiltUpArea { get; set; }
        public double? MinBuiltUpArea { get; set; }
        public double? MaxBuiltUpArea { get; set; }
        public Guid? BuiltUpAreaUnitId { get; set; } = Guid.Empty;
        public double? BuiltUpAreaInSqMtr { get; set; }
        public double? SaleableArea { get; set; }
        public double? MinSaleableArea { get; set; }
        public double? MaxSaleableArea { get; set; }
        public Guid? SaleableAreaUnitId { get; set; } = Guid.Empty;
        public double? SaleableAreaInSqMtr { get; set; }
        public float? BuiltUpAreaConversionFactor { get; set; }
        public float? SaleableAreaConversionFactor { get; set; }
        public string? DataConverted { get; set; }
        public List<Guid>? QualifiedByIds { get; set; }
        public string? ConfidentialNotes { get; set; }
        public double? NetArea { get; set; }
        public double? MinNetArea { get; set; }
        public double? MaxNetArea { get; set; }
        public Guid NetAreaUnitId { get; set; } = Guid.Empty;
        public double? PropertyArea { get; set; }
        public double? MinPropertyArea { get; set; }
        public double? MaxPropertyArea { get; set; }
        public Guid PropertyAreaUnitId { get; set; } = Guid.Empty;
        public string? UnitName { get; set; }
        public List<string>? ClusterName { get; set; }
        public List<string>? Nationality { get; set; }
        public List<string>? UnitNames { get; set; }
        public List<string>? CampaignNames { get; set; }
        public List<Purpose>? Purposes { get; set; }
        public int? ChildLeadsCount { get; set; }
        public bool? ShowPrimeLeads {  get; set; }
        public PossesionType? PossesionType { get; set; }
        public DateTime? FromPossesionDate { get; set; }
        public DateTime? ToPossesionDate { get; set; }
        public List<string>? LandLine { get; set; }
        public bool? ShowOnlyParentLeads { get; set; }

        public List<LeadType>? LeadType { get; set; }

        public List<string>? PropertyToSearch { get; set; }
        public List<string>? CountryCode { get; set; }
        public List<string>? AltCountryCode { get; set; }
        public List<Gender>? GenderTypes { get; set; }
        public List<MaritalStatusType>? MaritalStatuses { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public List<Guid>? UserIds { get; set; }
        public List<CallStatus>? CallStatuses { get; set; }
        public List<Domain.Enums.CallDirection>? CallDirections { get; set; }
        public OwnerSelectionType? OwnerSelection { get; set; }
        public string? TimeZoneId { get; set; }
        public TimeSpan? BaseUTcOffset { get; set; }
    }
    public class GetAllLeadsOnlyByNewFiltersRequest : GetAllLeadsParametersNewFilters, IRequest<PagedResponse<ViewLeadDto, string>>
    {


    }
    public class GetAllLeadsOnlyByNewFiltersRequestHandler : GetAllLeadsCommonHandler, IRequestHandler<GetAllLeadsOnlyByNewFiltersRequest, PagedResponse<ViewLeadDto, string>>
    {
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<Domain.Entities.UserDetails> _userDetails;

        public GetAllLeadsOnlyByNewFiltersRequestHandler(
            IDapperRepository dapperRepository,
            ILeadRepository efLeadRepository,
            ICurrentUser currentUser,
            ILeadRepositoryAsync leadRepositoryAsync,
            IRepositoryWithEvents<CustomMasterLeadStatus> masterLeadStatus,
            IRepositoryWithEvents<Domain.Entities.UserDetails> userDetails)
            : base(currentUser, dapperRepository, efLeadRepository, masterLeadStatus)
        {
            _leadRepositoryAsync = leadRepositoryAsync;
            _userDetails = userDetails;

        }

        public async Task<PagedResponse<ViewLeadDto, string>> Handle(GetAllLeadsOnlyByNewFiltersRequest request, CancellationToken cancellationToken)
        {
            try
            {
                if (request?.LeadTags?.Any() ?? false)
                {
                    request.TagFilterDto = GetLeadTagFilter(request.Adapt<GetAllLeadsByNewFiltersRequest>());
                    request.LeadTags = null;
                }
                var userId = _currentUser.GetUserId();
                var tenantId = _currentUser.GetTenant();
                var isAdmin = _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty).Result;
                if (request.IsGenManagerWithTeam ?? false && (request.GeneralManagerIds?.Any() ?? false))
                {
                    var generalManagerIds = await _dapperRepository.GeneralManagerAsync(request.GeneralManagerIds ?? new(), tenantId ?? string.Empty);
                    if (generalManagerIds?.Any() ?? false)
                    {
                        var subordinateIds = await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(generalManagerIds.ToList(), tenantId ?? string.Empty);
                        if (request.AssignTo == null)
                        {
                            request.AssignTo = new List<Guid>();
                        }
                        request.AssignTo.AddRange(subordinateIds);
                        request.AssignTo.AddRange(generalManagerIds);
                        request.AssignTo.AddRange(request.GeneralManagerIds);
                    }
                    if (request.AssignTo == null)
                    {
                        request.AssignTo = new List<Guid>();
                    }
                    request.AssignTo.AddRange(request.GeneralManagerIds);
                }
                else if ((request.GeneralManagerIds?.Any() ?? false))
                {
                    if (request.AssignTo == null)
                    {
                        request.AssignTo = new List<Guid>();
                    }
                    request.AssignTo.AddRange(request.GeneralManagerIds);

                }
                if (request?.DesignationsId?.Any() ?? false)
                {
                    var users = await _userDetails.ListAsync(new Lrb.Application.Dashboard.Web.Specs.GetUsersByDesignationIdSpec(request.DesignationsId));
                    var userIds = users.Select(i => i.UserId).ToList();
                    if (request.AssignTo == null)
                    {
                        request.AssignTo = new List<Guid>();
                    }
                    request.AssignTo.AddRange(userIds);

                }
                //request.UserType = request?.UserType ?? UserType.None;

                List<Guid> leadHistoryIds = new();
                List<Guid> subIds = new();


                try
                {
                    if (request?.AssignTo?.Any() ?? false)
                    {
                        if (request?.IsWithTeam ?? false)
                        {
                            subIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.AssignTo, tenantId ?? string.Empty)).ToList();
                        }
                        else
                        {
                            subIds = request?.AssignTo ?? new List<Guid>();
                        }
                    }
                    else
                    {
                        if (request?.IsOnlyReportees ?? false)
                        {
                            subIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty))?.ToList() ?? new();
                        }
                        else
                        {
                            subIds = (await _dapperRepository.GetSubordinateIdsAsync(userId, tenantId ?? string.Empty, request?.CanAccessAllLeads))?.ToList() ?? new();
                        }
                    }
                }
                catch (Exception ex)
                {
                    return new() { Message = ex.Message + "Something Went Wrong" };
                }
                if (request.IsDualOwnershipEnabled == null)
                {
                    request.IsDualOwnershipEnabled = await _dapperRepository.GetDualOwnershipDetails(tenantId ?? string.Empty);
                }
                var customStatus = await _customMasterLeadStatusRepo.ListAsync(cancellationToken);
                if (request.RadiusInKm != null)
                {
                    List<LeadIdWithLatLongDto> leadIdsWithLatLong = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<LeadIdWithLatLongDto>("LeadratBlack", "get_leads_with_lat_long", new
                    {
                        user_ids = subIds,
                        tenant_id = tenantId
                    })).ToList();
                    List<Guid> leadIdsWithinRange = new();

                    if (!string.IsNullOrEmpty(request.Latitude) && !string.IsNullOrEmpty(request.Longitude))
                    {
                        leadIdsWithinRange = leadIdsWithLatLong.Where(i =>
                                             (i != null && i.Latitude != null && i.Longitude != null) &&
                                             (request.Latitude != null && request.Longitude != null) &&
                        GeoLocationHelper.IsLocationWithinRange(double.TryParse(request.Latitude, out var requestLat) ? requestLat : 0.0,
                                                                                    double.TryParse(request.Longitude, out var requestLong) ? requestLong : 0.0,
                                                                                     double.TryParse(i.Latitude, out var leadLat) ? leadLat : 0.0,
                                                                                     double.TryParse(i.Longitude, out var leadLong) ? leadLong : 0.0, request.RadiusInKm * 1000 ?? 0.0)).Select(i => i.Id).ToList();

                        request.LeadIds = leadIdsWithinRange;
                    }
                }
                GetAllLeadsByNewFiltersRequest newRequest = request.Adapt<GetAllLeadsByNewFiltersRequest>();
                GetAllLeadsByNewFiltersRequest newRequestForCount = request.Adapt<GetAllLeadsByNewFiltersRequest>();
                var leads = _efLeadRepository.GetAllLeadsByNewFiltersForWebAsync(newRequest, subIds, userId, leadHistoryIds, customStatus, isAdmin: isAdmin).Result.ToList();
                //if (!(await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty)))
                //{
                //    leads.ForEach(lead => lead.Appointments = lead.Appointments != null && lead.Appointments.Any() ?
                //                      lead.Appointments.Where(appointment => appointment.UserId == lead.AssignTo).ToList() : new());
                //}
                (List<AppointmentType> appTypes, List<bool> appDoneStatuses) = _efLeadRepository.GetAppointmentTypes(request);
                if (request.AppointmentDoneByUserIds?.Any() ?? false)
                {
                    leads.ForEach(lead =>
                    {
                        if (lead?.Appointments?.Any() ?? false)
                        {
                            var uniqueAppointments = lead.Appointments.Where(i => i.UniqueKey != null && i.UniqueKey != Guid.Empty).DistinctBy(i => i.UniqueKey).ToList();
                            uniqueAppointments.AddRange(lead.Appointments.Where(i => i.UniqueKey == null || i.UniqueKey == Guid.Empty).ToList());
                            lead.Appointments = uniqueAppointments?.Where(i => request.AppointmentDoneByUserIds.Contains(i.CreatedBy) && appTypes.Contains(i.Type) && appDoneStatuses.Contains(i.IsDone))?.OrderBy(i => i.LastModifiedOn)?.ToList() ?? new();
                        }
                    });
                }
                else if (subIds != null && !(isAdmin))
                {
                    leads.ForEach(lead =>
                    {
                        if ((lead?.Appointments?.Any() ?? false) && lead.Appointments.Any(i => subIds.Contains(i.UserId)))
                        {
                            var appointmentsWithoutUniqueKey = lead.Appointments?.Where(i => i.UniqueKey == null || i.UniqueKey == default).ToList() ?? new();

                            var appointmentsWithUniqueKey = lead.Appointments?.Where(i => i.UniqueKey != null && i.UniqueKey != default && subIds.Contains(i.UserId))?.DistinctBy(i => i.UniqueKey)?.OrderBy(i => i.LastModifiedOn)?.ToList() ?? new();

                            appointmentsWithoutUniqueKey.AddRange(appointmentsWithUniqueKey);
                            lead.Appointments = appointmentsWithoutUniqueKey;
                        }
                        else
                        {
                            lead.Appointments = null;
                        }
                    });
                }
                else
                {
                    leads.ForEach(lead =>
                    {
                        if (lead?.Appointments?.Any() ?? false)
                        {
                            var appointmentsWithoutUniqueKey = lead.Appointments?.Where(i => i.UniqueKey == null || i.UniqueKey == default).ToList() ?? new();
                            var appointmentsWithUniqueKey = lead.Appointments?.Where(i => i.UniqueKey != null && i.UniqueKey != default)?.DistinctBy(i => i.UniqueKey).ToList() ?? new();

                            appointmentsWithoutUniqueKey.AddRange(appointmentsWithUniqueKey);
                            lead.Appointments = appointmentsWithoutUniqueKey;
                        }
                    });
                }
                var count = leads.Count();
                var totalCount = await _efLeadRepository.GetLeadsCountByNewFiltersForWebAsync(newRequestForCount, subIds, userId, leadHistoryIds, customStatus,isAdmin);
                List<ViewLeadDto> leadDtos = leads.Adapt<List<ViewLeadDto>>();
                return new(leadDtos, totalCount);
            }
            catch(Exception ex)
            {
                return new() { Message = ex.Message + "Something Went Wrong" };
            }
        }
    }
    public class SortingDto
    {
        public string? ColumnName { get; set; }
        public bool? IsAscending { get; set; }
    }
    public class Date
    {
        public DateType? MultiDateType { get; set; }
        public DateTime? MultiFromDate { get; set; }
        public DateTime? MultiToDate { get; set; }
    }
}
