﻿namespace Lrb.Application.Project.Mobile
{
    public class ProjectByIdSpec : Specification<Domain.Entities.Project>
    {
        public ProjectByIdSpec(Guid id)
        {
            Query.Where(i => !i.IsDeleted).Where(i => !i.IsArchived)
              .Include(i => i.UnitTypes)
                  .ThenInclude(i => i.Attributes)
              .Include(i => i.UnitTypes)
                  .ThenInclude(i => i.MasterUnitType)
              .Include(i => i.UnitTypes)
                  .ThenInclude(i => i.UnitInfoGalleries)
              .Include(i => i.Blocks)
              .Include(i => i.ProjectGalleries)
              .Include(i => i.ProjectType)
              .Include(i => i.MonetaryInfo)
              .Include(i => i.Address)
                  .ThenInclude(i => i.Location)
                      .ThenInclude(i => i.Zone)
                          .ThenInclude(i => i.City)
              .Include(i => i.BuilderDetail)
              .Include(i => i.AssociatedBanks)
              .Include(i => i.UserAssignment)
              .Include(i => i.Amenities)
              .Where(i => i.Id == id);
        }
    }

    public class GetProjectUnitByProjectIdSpecs : Specification<Domain.Entities.Project>
    {
        public GetProjectUnitByProjectIdSpecs(Guid id)
        {
            Query.Where(i => !i.IsDeleted && !i.IsArchived)
                .Include(i => i.UnitTypes)
                .Where(i => i.Id == id);
        }
    }

    public class GetProjectByIdForUnitAndBlockSpecs : Specification<Domain.Entities.Project>
    {
        public GetProjectByIdForUnitAndBlockSpecs(Guid id)
        {
            Query.Where(i => !i.IsDeleted && !i.IsArchived)
                .Where(i => i.Id == id);
        }
    }

  
    public class ProjectByIdsSpec : Specification<Domain.Entities.Project>
    {
        public ProjectByIdsSpec(List<Guid> ids)
        {
            Query.Where(i => !i.IsDeleted && ids.Contains(i.Id));

        }
    }
    public class GetProjectByIdSpec : Specification<Domain.Entities.Project>
    {
        public GetProjectByIdSpec(Guid id)
        {
            Query.Where(i => !i.IsDeleted).Where(i => !i.IsArchived)
               .Include(i => i.Address)
                   .ThenInclude(i => i.Location)
                       .ThenInclude(i => i.Zone)
                           .ThenInclude(i => i.City)
               .Where(i => i.Id == id);
        }
    }
    public class GetCustomAmenitiesByIdsSpec : Specification<CustomMasterAmenity>
    {
        public GetCustomAmenitiesByIdsSpec(List<Guid> amenitiesIds)
        {
            Query.Where(i => !i.IsDeleted).Where((i => amenitiesIds.Contains(i.Id) || amenitiesIds.Contains(i.MasterAmenityId ?? Guid.Empty)));
        }
    }
    public class ProjectsByIdsSpec : Specification<Domain.Entities.Project>
    {
        public ProjectsByIdsSpec(List<Guid> ids)
        {
            Query.Where(i => !i.IsDeleted).Where(i => !i.IsArchived)
               .Include(i => i.UnitTypes)
                   .ThenInclude(i => i.Attributes)
               .Include(i => i.UnitTypes)
                   .ThenInclude(i => i.MasterUnitType)
               .Include(i => i.UnitTypes)
                   .ThenInclude(i => i.UnitInfoGalleries)
               .Include(i => i.Blocks)
               .Include(i => i.ProjectGalleries)
               .Include(i => i.ProjectType)
               .Include(i => i.MonetaryInfo)
               .Include(i => i.Address)
                   .ThenInclude(i => i.Location)
                       .ThenInclude(i => i.Zone)
                           .ThenInclude(i => i.City)
               .Include(i => i.BuilderDetail)
               .Include(i => i.AssociatedBanks)
               .Include(i => i.UserAssignment)
               .Where(i => ids.Contains(i.Id));
        }
    }
}
