﻿using DocumentFormat.OpenXml.Spreadsheet;
using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.DataManagement.Mobile.Dtos;
using Lrb.Application.DataManagement.Mobile.Specs;
using CustomFilter = Lrb.Domain.Entities.CustomFilter;

namespace Lrb.Application.DataManagement.Mobile.Requests
{
    public class GetAllProspectsByCustomFiltersRequest : GetAllProspectFilterParameter, IRequest<PagedResponse<ViewProspectDto, string>>
    {

    }

    public class GetAllProspectsByCustomFiltersRequestHandler : IRequestHandler<GetAllProspectsByCustomFiltersRequest, PagedResponse<ViewProspectDto, string>>
    {
        private readonly IRepositoryWithEvents<Prospect> _prospectRepo;
        private readonly ICurrentUser _currentUser;
        private readonly IProspectRepository _efProspectRepository;
        private readonly IDapperRepository _dapperRepository;
        private readonly IRepositoryWithEvents<CustomFilter> _customfilterRepo;
        private readonly IRepositoryWithEvents<CustomProspectStatus> _customProspectRepo;
        public GetAllProspectsByCustomFiltersRequestHandler(IRepositoryWithEvents<Prospect> prospectRepo,
            ICurrentUser currentUser,
            IProspectRepository efProspectRepository,
            IDapperRepository dapperRepository,
            IRepositoryWithEvents<CustomFilter> customfilterRepo,
            IRepositoryWithEvents<CustomProspectStatus> customProspectRepo)
        {
            _prospectRepo = prospectRepo;
            _currentUser = currentUser;
            _efProspectRepository = efProspectRepository;
            _dapperRepository = dapperRepository;
            _customfilterRepo = customfilterRepo;
            _customProspectRepo = customProspectRepo;
        }
        public async Task<PagedResponse<ViewProspectDto, string>> Handle(GetAllProspectsByCustomFiltersRequest request, CancellationToken cancellationToken)
        {
            var userId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            List<Guid> subIds = new();
            var isAdmin = await _dapperRepository.IsAdminV2Async(userId, tenantId ?? string.Empty);
            CustomFilter? filter = null;
            List<Prospect>? prospects = null;
            int totalCount = default;
            try
            {
                if (request?.AssignTo?.Any() ?? false)
                {
                    if (request?.IsWithTeam ?? false)
                    {
                        subIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.AssignTo, tenantId ?? string.Empty)).ToList();
                    }
                    else
                    {
                        subIds = request?.AssignTo ?? new List<Guid>();
                    }
                }
                else
                {
                    subIds = (await _dapperRepository.GetSubordinateIdsAsync(userId, tenantId ?? string.Empty, request?.CanAccessAllProspects,isAdmin))?.ToList() ?? new();
                }
            }
            catch
            {

            }
            if (!(request?.CustomFilterIds?.Any() ?? false))
            {
                filter = await _customfilterRepo.FirstOrDefaultAsync(new GetProspectCustomFiltersSpec(userId, isAdmin), cancellationToken);
            }
            else
            {
                filter = await _customfilterRepo.FirstOrDefaultAsync(new GetProspectCustomFiltersSpec(request.CustomFilterIds ?? new()), cancellationToken);
            }
            List<CustomProspectStatus> statuses = null;
            try
            {
                if (!string.IsNullOrWhiteSpace(request.ProspectSearch) && request.PropertyToSearch != null && request.PropertyToSearch.Contains("Status"))
                {
                    statuses = await _customProspectRepo.ListAsync(new Web.Request.GetProspectStatusByNameSpecs(request.ProspectSearch));
                }
            }
            catch (Exception ex)
            {
            }
            var tasks1 = new Task[]
            {
                Task.Run(async () => prospects = (await _efProspectRepository.GetAllProspectsByCustomFiltersForMobileAsync(filter ?? new CustomFilter(), request.Adapt<GetAllProspectFilterParameter>(), subIds, userId,isAdmin, statuses)).ToList()),
                Task.Run(async () => totalCount = await _efProspectRepository.GetProspectsCountByCustomFiltersForMobileAsync(filter ?? new CustomFilter(), request.Adapt<GetAllProspectFilterParameter>(), subIds, userId,isAdmin, prospectStatuses:statuses)),
            };
            await Task.WhenAll(tasks1);
            List<ViewProspectDto> prospectsView = prospects?.Adapt<List<ViewProspectDto>>() ?? new();
            return new(prospectsView, totalCount);
        }
    }
}
