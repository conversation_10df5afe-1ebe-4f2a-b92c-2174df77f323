﻿using Lrb.Application.Common.ServiceBus;
using Lrb.Application.Dashboard.Web;
using Lrb.Domain.Entities.Marketing;
using Newtonsoft.Json;

namespace Lrb.Application.ChannelPartner.Web.Request
{
    public class RunAWSBatchForMarketingChannelPartnerRequest : IRequest<Response<Guid>>
    {
        public List<string>? SearchByName { get; set; }
        public string? SearchText { get; set; }
        public DateType? DateType { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public List<string>? Localites { get; set; }
        public List<string>? States { get; set; }
        public List<string>? Cities { get; set; }
        public List<string>? Location { get; set; }
        public string? LeadsMinCount { get; set; }
        public string? LeadsMaxCount { get; set; }
        public string? ProspectMinCount { get; set; }
        public string? ProspectMaxCount { get; set; }
        public ReportPermission? ExportPermission { get; set; }
        public MarketingExportType ExportType { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = int.MaxValue;
        public List<string>? ToRecipients { get; set; } = new();
        public List<string>? CcRecipients { get; set; } = new();
        public List<string>? BccRecipients { get; set; } = new();
        public string? FileName { get; set; }
        public string? TimeZoneId { get; set; }
        public TimeSpan BaseUTcOffset { get; set; }
    }
    public class RunAWSBatchForMarketingChannelPartnerRequestHandler : IRequestHandler<RunAWSBatchForMarketingChannelPartnerRequest, Response<Guid>>
    {

        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<ExportMarketingTracker> _exportRepo;
        public const string TYPE = "exportmarketingchannelpartner";
        private readonly IServiceBus _serviceBus;

        public RunAWSBatchForMarketingChannelPartnerRequestHandler(
            ICurrentUser currentUser,
            IRepositoryWithEvents<ExportMarketingTracker> exportRepo,
            IServiceBus serviceBus)
        {
            _currentUser = currentUser;
            _exportRepo = exportRepo;
            _serviceBus = serviceBus;
        }

        public async Task<Response<Guid>> Handle(RunAWSBatchForMarketingChannelPartnerRequest request, CancellationToken cancellationToken)
        {
            try
            {
                ExportMarketingTracker tracker = new();
                tracker.Request = JsonConvert.SerializeObject(request);
                tracker.ToRecipients = request.ToRecipients;
                tracker.CcRecipients = request.CcRecipients;
                tracker.BccRecipients = request.BccRecipients;
                tracker.ExportType = request.ExportType;
                tracker.Type = TYPE;
                var tenantId = _currentUser.GetTenant();
                var currentUserId = _currentUser.GetUserId();
                await _exportRepo.AddAsync(tracker, cancellationToken);
                //Submit a job in AWS Batch
                InputPayload input = new(tracker.Id, tenantId ?? string.Empty, currentUserId, TYPE);
                var stringArgument = JsonConvert.SerializeObject(input);
                var cmdArgs = new List<string>() { stringArgument };
                await _serviceBus.RunExcelUploadJobAsync(cmdArgs);
                return new(tracker.Id);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Something went wrong.");
            }
        }
    }
}
