namespace Lrb.Application.Common.Persistence;

using Lrb.Application.AutoDialer.Web.Dtos;
using Lrb.Application.Dashboard.Web.Dtos;
using Lrb.Application.DataManagement.Mobile.Dtos;
using Lrb.Application.GlobalSettings.Common;
using Lrb.Application.GlobalSettings.Mobile.Dto;
using Lrb.Application.Integration.Web.Dtos;
using Lrb.Application.Lead.Mobile.Dtos.v1;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Lead.Web.Export;
using Lrb.Application.Multitenancy;
using Lrb.Application.OrgProfile.Web.Dtos;
using Lrb.Application.Project.Web;
using Lrb.Application.Property.Web;
using Lrb.Application.Property.Web.Dtos;
using Lrb.Application.PushNotification;
using Lrb.Application.PushNotification.Mobile.Requests;
using Lrb.Application.Subscription.Web.Dtos;
using Lrb.Application.TempProject.Dtos;
using Lrb.Application.UserDetails.Web;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Multitenancy;
using Lrb.Application.Lead.Web.Export;
using Lrb.Application.Property.Web;
using Lrb.Application.DataManagement.Mobile.Dtos;
using Lrb.Application.DataManagement.Mobile.Requests;
using Lrb.Application.Lead.Mobile.Dtos.v1;
using Lrb.Application.Project.Web.Dtos;
using Lrb.Application.GlobalSettings.Mobile.Dto;
using Lrb.Application.AutoDialer.Web.Dtos;
using Lrb.Application.PushNotification.Mobile.Dtos;
using Lrb.Application.UserDetails.Web.Dtos;
using System.Data;
using Lrb.Application.Common.GoogleAds;

public interface IDapperRepository : ITransientService
{
    /// <summary>
    /// Get an <see cref="IReadOnlyList{T}"/> using raw sql string with parameters.
    /// </summary>
    /// <typeparam name="T">The type of the entity.</typeparam>
    /// <param name="sql">The sql string.</param>
    /// <param name="param">The paramters in the sql string.</param>
    /// <param name="transaction">The transaction to be performed.</param>
    /// <param name="cancellationToken">The <see cref="CancellationToken"/> to observe while waiting for the task to complete.</param>
    /// <returns>Returns <see cref="Task"/> of <see cref="IReadOnlyCollection{T}"/>.</returns>
    Task<IReadOnlyList<T>> QueryAsync<T>(string sql, object? param = null, IDbTransaction? transaction = null, CancellationToken cancellationToken = default)
    where T : class, IEntity;

    /// <summary>
    /// Get an <see cref="IReadOnlyList{T}"/> using raw sql string with parameters.
    /// </summary>
    /// <typeparam name="T">The type of the entity.</typeparam>
    /// <param name="sql">The sql string.</param>
    /// <param name="transaction">The transaction to be performed.</param>
    /// <param name="cancellationToken">The <see cref="CancellationToken"/> to observe while waiting for the task to complete.</param>
    /// <returns>Returns <see cref="Task"/> of <see cref="IReadOnlyCollection{T}"/>.</returns>
    Task<IReadOnlyList<T>> QueryAsync<T>(string sql, IDbTransaction? transaction = null, CancellationToken cancellationToken = default)
    where T : class;

    /// <summary>
    /// Get a <typeparamref name="T"/> using raw sql string with parameters.
    /// </summary>
    /// <typeparam name="T">The type of the entity.</typeparam>
    /// <param name="sql">The sql string.</param>
    /// <param name="param">The paramters in the sql string.</param>
    /// <param name="transaction">The transaction to be performed.</param>
    /// <param name="cancellationToken">The <see cref="CancellationToken"/> to observe while waiting for the task to complete.</param>
    /// <returns>Returns <see cref="Task"/> of <typeparamref name="T"/>.</returns>
    Task<T?> QueryFirstOrDefaultAsync<T>(string sql, object? param = null, IDbTransaction? transaction = null, CancellationToken cancellationToken = default)
    where T : class, IEntity;

    /// <summary>
    /// Get a <typeparamref name="T"/> using raw sql string with parameters.
    /// </summary>
    /// <typeparam name="T">The type of the entity.</typeparam>
    /// <param name="sql">The sql string.</param>
    /// <param name="param">The paramters in the sql string.</param>
    /// <param name="transaction">The transaction to be performed.</param>
    /// <param name="cancellationToken">The <see cref="CancellationToken"/> to observe while waiting for the task to complete.</param>
    /// <returns>Returns <see cref="Task"/> of <typeparamref name="T"/>.</returns>
    Task<T> QuerySingleAsync<T>(string sql, object? param = null, IDbTransaction? transaction = null, CancellationToken cancellationToken = default)
    where T : class, IEntity;

    /// <summary>
    /// Call any Stored Procedure
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="schemaName"></param>
    /// <param name="spName"></param>
    /// <param name="param"></param>
    /// <returns>Enumerable of "T"</returns>
    Task<IEnumerable<T>> QueryStoredProcedureAsync<T>(string schemaName, string spName, object param);
    Task<T> QueryStoredProcedureFirstOrDefaultAsync<T>(string schemaName, string spName, object param);
    Task<IEnumerable<T>> QueryStoredProcedureV2Async<T>(string schemaName, string spName, object param);
    Task<IEnumerable<T>> QueryStoredProcedureV3Async<T>(string schemaName, string spName, object param, List<string> paramNames);
    Task<int> QueryStoredProcedureCountV2Async(string schemaName, string spName, object param);
    Task<IEnumerable<T>> QueryStoredProcedureWithNewConnAsync<T>(string schemaName, string spName, object param);
    Task<int> QueryStoredProcedureCountAsync(string schemaName, string spName, object param);
    Task<IEnumerable<Guid>> GetSubordinateIdsAsync(Guid userId, string tenantId, bool? viewAllLeads, bool? isAdmin = null);
    Task<IEnumerable<Guid>> GetSubordinateIdsV2Async(Guid userId, string tenantId, bool? viewAllLeads, bool? isAdmin = null);
    Task<IEnumerable<Guid>> GetSubordinateIdsAsync(List<Guid> userIds, string tenantId);
    Task<IEnumerable<Guid>> GetSubordinateIdsWithOnlyReporteesAsync(List<Guid> userIds, string tenantId);
    Task<IEnumerable<Guid>> GetSubordinateIdsWithOnlyReporteesV2Async(List<Guid> userIds, string tenantId);
    Task<IEnumerable<Guid>> GetLeadHistoryIdsByMeetingOrVisitStatusAsync(string tenantId, List<MeetingOrVisitCompletionStatus> statuses, DateTime? formDate, DateTime? toDate);
    Task<string> GetMeetingAndVisitReportAsync(string tenantId, int pagesize, int pagenumber, string? userName);
    Task<IEnumerable<string>> GetAllLeadsAddressesAsync(string tenantId);
    Task<IEnumerable<string>> GetAllLeadsZonesAsync(string tenantId);
    Task<IEnumerable<string>> GetAllLeadsCitiesAsync(string tenantId);
    Task<IEnumerable<string>> GetAllLeadsStatesAsync(string tenantId);
    Task<IEnumerable<string>> GetAllLeadsLocalitesAsync(string tenantId);
    Task<IEnumerable<T>> GetAllLeadsSubSourceAsync<T>(string tenantId);
    Task<IEnumerable<SourceDtoV3>> GetAllLeadsSubSourceV3Async(string tenantId);
    Task<IEnumerable<SourceDtoV3>> GetAllLeadsSourceWithSubSourceV3Async(string tenantId);
    Task<IEnumerable<T>> GetAllIntegrationSubSourceAsync<T>(string tenantId);
    Task<IEnumerable<Guid>> GetLeadHistoryIdsByBaseLeadStatus(string tenantId, string status);
    Task<bool> IsAdminAsync(Guid userId, string tenantId);
    Task<bool> IsAdminV2Async(Guid userId, string tenantId);
    Task<bool> IsAnyAdminAsync(List<Guid> userIds, string tenantId);
    Task<List<string>> GetAgencyNamesAsync(string tenantId);
    Task<List<string>> GetAssignedAgencyNamesInLeadAsync(string tenantId);
    Task<List<string>> GetAssignedAgencyNamesInLeadV2Async(string tenantId);
    Task<List<string>> GetOwnerNamesInAllPropertiesAsync(string tenantId);

    Task<IEnumerable<Guid>> GetSubordinateIdsPOCAsync(Guid userId, string tenantId);
    Task<IEnumerable<Guid>> GetSubordinateIdsPOCAsync(List<Guid> userIds, string tenantId);
    Task<IEnumerable<Guid>> GetSubordinateIdsWithOnlyReporteesPOCAsync(List<Guid> userIds, string tenantId);
    Task<IEnumerable<Guid>> GetLeadHistoryIdsByBaseLeadStatusPOC(string tenantId, string status);

    Task<DateTime> GetLastModifiedDateByAgencyNameAsync(string tenantId);
    Task<int> GetLeadSourceUpdateStatusAsync(Guid leadId, string tenantId);
    Task<List<DeviceAndTokenDto>> GetDeviceIdsAndTokensAsync(List<Guid> userIds);
    Task<int> UpdateChildLeadsCount(Guid leadId, int count);
    Task<int> UpdateChildLeadsCountInHistory(Guid leadId, int childsCount, int version);
    Task<int> GetLeadById(Guid leadId);
    Task<int> GetLeadHistoryById(Guid leadId);
    Task<KeyValuePair<bool, List<Guid>>> GetSubordinateIdsWithAdminAsync(Guid userId, string tenantId, bool? viewAllLeads = null);
    Task<List<AttendanceLog>> GetAttendanceLogsByDateRangeAsync(DateTime startDate, DateTime endDate, Guid userId, string tenantId);
    Task<AttendanceLog> GetTodayAttendanceLogAsync(Guid userId, string tenantId);
    Task<int> GetFacebookIntegrationAdsAndFormCount(string addName, string formName, string tenantId);
    Task<IEnumerable<string>> GetAllChannelPartnerAsync(string tenantId, string serchByName);
    Task<Lrb.Application.Lead.Mobile.v2.ViewLeadAnonymousDto> GetLeadByIdAnonymousAsync(Guid leadId);
    public Task<int> GetTotalNotificationsCountAsync(Guid userId,string? tenantId=null);
    public Task<int> GetTotalUnOpenedNotificationsCountAsync(Guid userId, string? tenantId=null);
    Task<IEnumerable<Guid>> GetAllUserIdsAsync(string tenantId);
    Task<Lrb.Application.OrgProfile.Web.Dtos.SubscriptionInfoDto> GetTenantSubsciptionInfoDetailsAsync(string tenant);
    Task<IEnumerable<TransactionDetailsDto>> GetAllTransactionDetails(string tenant);
    Task<bool> CheckUserMoreThanLicenseBought(string tenantId);
    Task<List<AdminUserDto>> GetAdminsNameAndNumberAsync(string tenantId);
    Task<string> GetEmailTemplateBodyAsync();
    Task<IEnumerable<Guid>> GetAllAdminIdsAsync(string tenantId);
    Task<IEnumerable<Guid>> GetUserIdsWithoutAdminAsync(string tenantId);
    Task<FacebookAuthResponse> GetFacebookAccountAsync(string tenantId, Guid id);
    Task<FacebookConnectedPageAccount> GetFacebookPageAccountAsync(string tenantId, string pageId);
    Task<IEnumerable<T>> GetAllProspectSubSourceAsync<T>(string tenantId);
    Task<IEnumerable<string>> GetAllProspectAddresses(string tenantId);
    Task<IEnumerable<string>> GetAllProspectCities(string tenantId);
    Task<IEnumerable<string>> GetAllPropertiesAddressesAsync(string tenantId);
    Task<IEnumerable<T>> GetPropertiesWithIdsAndNames<T>(string tenantId);
    //=========
    //Task<IEnumerable<string>> GetAllPropertiesAddressesAsync(string tenantId);
    Task<List<PropertyIdWithLatLongDto>> GetMatchingPropertyWithLatLong(string tenantId);
    Task<LeadCallLog.Mobile.TenantDto> GetTenantIdByDeviceUDIDAsync(string v);
    Task<bool> GetDualOwnershipDetails(string tenantId);
    Task<bool> V2GetDualOwnershipDetails(string tenantId);
    Task<DateTime> GetLastModifiedDate(string tenantId);
    Task<IEnumerable<Guid>> GetSubordinateIdsForDashboardAsync(List<Guid> userIds, string tenantId);
    Task<List<SetWatchForGmailDto>> GetGmailDetailsAsync();
    Task<bool> UpdateGmailHistoryIdAsync(string historyId, Guid id);
    Task<IEnumerable<string>> GetStoredCurrencyAsync(string tenantId);
    Task<IEnumerable<string>> GetProspectStoredCurrencyAsync(string tenantId);
    Task<IEnumerable<string>> GetPropertyCurrencyAsync(string tenantId);

    Task<string> GetStatusNameByBaseIdAsync(Guid baseId, string tenantId);
    Task<List<string>> GetAllProjectAddressesAsync(string tenantId);
    Task<IEnumerable<string>> GetAllProspectStatesAsync(string tenantId);
    Task<List<string>> V2GetAdminIdsAsync(string tenantId);
    Task<IEnumerable<string>> GetProjectCurrencyAsync(string tenantId);
    Task<IEnumerable<string>> GetProjectGalleryImageKeysAsync(string tenantId);
    Task<IEnumerable<Guid>> GetSubordinateIdsWithColumnNameAsync(List<Guid> userIds, string tenantId, string? columnName);
    Task<Guid> GetLeadDetails(string contactNo);
    Task<List<LeadForWANotiification>> GetLeadsCountAsync(List<Guid> ids, string tenantId);
    Task<IEnumerable<T>> QueryStoredProcedureV3Async<T>(string schemaName, string spName, object param);
    Task<IEnumerable<string>> GetAllUplodTypeName(string tenantId);
    Task<IEnumerable<GeneralManagerUserDto>> GetAllGeneralManagersAsync(string tenantId);
    Task<List<string>> GetAllAgencyNamesAsync(string tenantId);

    Task<List<string>> GetAllChannelPartnerNamesAsync(string tenantId);
    Task<int> GetLeadCount(string tenantId, Guid id);
    Task<WAOTPTemplateInfo> GetOTPWhatsAppInfoAsync();
    Task<IEnumerable<string>> GetAllPropertyMicrositeSerialNumbers(string tenantId);
    Task<IEnumerable<Guid>> GeneralManagerAsync(List<Guid> userIds, string tenantId);
    Task<IEnumerable<UserStatuses>> UserStatusesAsync(Guid userId, string tenantId, bool IsAdmin, bool? IsTeamPresent = null);

    Task<IEnumerable<string>> GetAllAgenciesAddressesAsync(string tenantId);
    Task<IEnumerable<string>> GetAllChannelPartnerAddressesAsync(string tenantId);
    Task<List<string>> GetAdditionalPropertyValuesAsync(string tenantId, string key);
    Task<IEnumerable<ProjectLeadCountDto>> GetLeadsCountByProjectIdsAsync(string tenantId, List<Guid> projectIds);
    Task<IEnumerable<ProjectLeadCountDto>> GetProspectsCountByProjectIdsAsync(string tenantId, List<Guid> projectIds);
    Task<IEnumerable<PropertyLeadCountDto>> GetLeadsCountByPropertyIdsAsync(string tenantId, List<Guid> propertyIds);
    Task<IEnumerable<PropertyLeadCountDto>> GetProspectsCountByPropertyIdsAsync(string tenantId, List<Guid> propertyIds);
    Task<TransactionInfoViewDto> GetTransactionByIdAsync(string orderId);

    Task<IEnumerable<string>> GetAllAmenitiesForPropertyAsync(string tenantId, List<Guid>? ids);
    Task<IEnumerable<string>> GetAllAmenitiesForProjetAsync(string tenantId, List<Guid>? Ids);
    Task<List<AttendanceLog>> GetAttendanceLogsByDateRangeWithTimeZoneAsync(DateTime startDate, DateTime endDate, Guid userId, string tenantId, string baseUtcOffset);
    Task<int> UpdatePaymentTransactionInfoAsync(TransactionInfoViewDto transactionInfo, CashfreePaymentDto cashfreePaymentDto);
    Task<bool> CheckAssignToByLeadIdAsync(string tenantId, Guid id, List<Guid> subIds);
    Task<bool> CheckAssignToByProspectIdAsync(string tenantId, Guid id, List<Guid> subIds);
    Task<IEnumerable<Guid>> GetSubordinateIdsByUserIdAsync(Guid userId, string tenantId);
    Task<List<string>> GetAllSubCommunitiesForListingManagement(string tenantId);
    Task<List<string>> GetAllCommunitiesForListingManagement(string tenantId);
    Task<IEnumerable<string>> GetAllLeadsCountriesAsync(string tenantId);
    Task<IEnumerable<string>> GetAllLeadsCommunitiesAsync(string tenantId);
    Task<IEnumerable<string>> GetAllLeadsSubCommunitiesAsync(string tenantId);
    Task<IEnumerable<string>> GetAllLeadsTowerNamesAsync(string tenantId);
    Task<IEnumerable<string>> GetAllLeadsPostalCodeAsync(string tenantId);

    string GetConnectionStringAsync();
    Task<IEnumerable<DuplicateDto>> CheckDuplicateLeadsAsync(List<string> contactNos, List<string>? altNumbers, string? tenantId);
    Task<IEnumerable<DuplicateDto>> CheckDuplicatePropsectsAsync(List<string> contactNos, List<string>? altNumbers, string? tenantId);
    Task<IEnumerable<string>> GetNotificationTrackersJobId(string tenantId, Guid entityId, DateTime? scheduledDate = null);
    Task<Lrb.Domain.Entities.Payment> GetPaymentByOrderId(string? orderId);
    Task<IEnumerable<UserBasicInfoDto>> GetUserDetailsAsync(string tenantId, List<Guid> ids);
    Task<IEnumerable<Guid>> GetLatestDuplicateLeadsByContactNos(List<string> contactNos);
    Task<string> GetDisplayIndexPrefixByTenantIdAsync(string tenantId);
    Task<long> GetLeadMaxSerialNumberAsync(string prefix);
    Task<long> GetProjectMaxSerialNumberAsync(string prefix);
    Task<long> GetPropertiesMaxSerialNumberAsync(string prefix);
    Task<IEnumerable<T>> QueryStoredProcedureFromReadReplicaAsync<T>(string schemaName, string spName, object param, int? commandTimeout = 60);
    Task<int> QueryStoredProcedureCountFromReadReplicaAsync(string schemaName, string spName, object param, int? commandTimeout = 60);
    Task<IEnumerable<string>> GetAllProspectCountriesAsync(string tenantId);
    Task<IEnumerable<string>> GetAllProspectCommunitiesAsync(string tenantId);
    Task<IEnumerable<string>> GetAllProspectSubCommunitiesAsync(string tenantId);
    Task<IEnumerable<string>> GetAllProspectTowerNamesAsync(string tenantId);
    Task<IEnumerable<string>> GetAllProspectPostalCodeAsync(string tenantId);
    Task<IEnumerable<string>> GetAllProspectsLocalitesAsync(string tenantId);
    Task<IEnumerable<string>> GetAllProspectsNationalitiesAsync(string tenantId);
    Task<IEnumerable<string>> GetAllLeadsNationalitiesAsync(string tenantId);
    Task<IEnumerable<string>> GetAllProspectUnitNameAsync(string tenantId);
    Task<IEnumerable<string>> GetAllLeadUnitNameAsync(string tenantId);
    Task<IEnumerable<string>> GetAllProspectClusterNameAsync(string tenantId);
    Task<IEnumerable<string>> GetAllLeadClusterNameAsync(string tenantId);
    Task<List<string>> GetAllCampaignsNamesAsync(string tenantId);
    Task<List<string>> GetAllProjectNamesAsync(string tenantId);
    Task<List<string>> GetAllPropertyTitleAsync(string tenantId);
    Task<List<ParentLeadDto>> GetAllParentLeadInfo(string tenantId, List<Guid>? rootIds);
    Task<bool> UpdateLeadHistory(List<Guid> ids);
    Task<bool> UpdateSecLeadHistory(List<Guid> ids, Guid CurrentUser, string username);
    Task<List<T>> GetAllLeadPropertyInfo<T>(string tenantId);
    Task<List<T>> GetAllLeadProjectInfo<T>(string tenantId);
    Task<IEnumerable<string>> GetAllUplodTypeNameOfProspectAsync(string tenantId);
    Task<List<string>> GetAllAmenityCategoryAsync();
    Task<bool> CreateDifferentDatabseTenantInfo(CreateTenantRequest request, string connectionString);
    Task<bool> UpdatePropertyAssignentAsync(Guid UserId);
    Task<bool> UpdatePropertyListingOnBehalfAsync(Guid UserId);
    Task<IEnumerable<string>> GetAllLeadLandLineDetailsAsync(string tenantId);
    Task<IEnumerable<string>> GetAllProspectLandLineDetailsAsync(string tenantId);


    Task<IEnumerable<Lrb.Application.Lead.Mobile.SourceDtoV3>> GetAllLeadsSourceWithSubSourceV4Async(string tenantId, bool? isEnabled = null);
    Task<IEnumerable<T>> GetAllProspectSubSourceV2Async<T>(string tenantId, bool? isEnabled = null);
    Task ExecuteQueryAsync(string query);
    Task<IEnumerable<Guid>> GetLeadIdsBySourceValueAsync(string tenantId, int sourceValue, CancellationToken cancellationToken);
    Task<List<Device>> GetDeviceInfo(string tenantId, List<Guid>? userIds = null);
    Task<IEnumerable<UserDetailsDto>> GetUserBasicDetailsAsync(List<Guid>? userIds, string tenantId);
    Task<bool> UpdateLead(Guid ids);
    Task<List<Guid>> GetManagerIdsAsync(string tenantId);
    Task<Guid?> GetGeneralManagerIdAsync(string userId);
    Task<IEnumerable<string>> GetCountryCode(string tenantId, string columName, string tableName);
    Task<List<T>> GetAllUserProjectInfo<T>(string tenantId);
    Task<List<T>> GetAllUserPropertyInfo<T>(string tenantId);
    Task<string> GetThirdPartyIdByUserId(Guid userId, string tenantId);
    Task<string> GetUserNameByThirdPartyId(int thirdPartyId, string tenantId);
    Task<List<CustomPropertyAttributeDto>?> GetAttributeDetails(string tenantId);
    Task<List<LeadCommunication>?> GetLeadsCommunicationsByLeadIds(string tenantId, Guid userId, List<Guid> leadIds, bool isAdmin);
    Task<List<ProspectCommunication>?> GetProspectsCommunicationsByProspectsIds(string tenantId, Guid userId, List<Guid> prospectIds, bool isAdmin);
    Task<List<LeadNotesHistoryDto>> GetLeadNotesHistoryByLeadId(Guid leadId, string tenantId, List<Guid>? userIds = null);
    Task<Guid> GetPropertyListingIdBySerialNoOrCustomRefNo(string refId, string tenantId);
    Task<DateTime> GetProspectStatusLastModifiedDate(string tenantId);
    Task<IEnumerable<T>> QueryStoredProcedureFromMainDbAsync<T>(string schemaName, string spName, object param, int? commandTimeout = 60);
    Task<List<GetAllOfflineProspectsDto>> GetAllOfflineProspectsAsync(string tenantId, Guid? userId, bool sendOnlyAssignedLeads, DateTime? dateRangeFrom = null, DateTime? dateRangeTo = null, int? pageNumber = null, int? pageSize = null);
    Task<List<GetAllOfflineLeadsDto>> GetAllOfflineLeadsAsync(string tenantId, Guid? userId, bool sendOnlyAssignedLeads, DateTime? dateRangeFrom = null, DateTime? dateRangeTo = null, int? pageNumber = null, int? pageSize = null, bool? offlineLeads = false);
    Task<AssignedUserDetailsDto> GetAssignToDetailsByContactNo(string? tenantId, string? contactNo);
    Task<IEnumerable<T>> GetProjectsWithIdsAndNames<T>(string tenantId);
    Task<List<ProspectCommunication>?> GetProspectsCommunicationsForHistory(string tenantId, Guid prospectId, List<Guid>? userIds);
    Task<CallSettingsDto> GetCallSettings(string tenantId);
    Task<bool> UpdateLatModifiedDateAsync(string tenantId, int entityType);
    Task<bool> SoftDeleteEmailTemplates(Guid emailApiIntegrationId, Guid currentUserId);

    Task<List<ViewUserBasicDetailsDto>> GetPaginatedUserBasicInfoAsync(string tenantId, int? pageNumber = 1, int? pageSize = 10);
    Task<FullUserViewDto> GetFullUserByTenantAsync(Guid userId, string tenantName, CancellationToken cancellationToken);
    Task<AutoDialerLeadDetailsDto> GetOngoingCallLeadDetails(string tenantId, Guid userId);
    Task<List<PushNotificationRecordsDto>> GetPushNotificationRecordsAsync(string tenantId, List<Guid> uniqueNotificationIds, Guid currentUserId);
    Task<List<NotificationDto>> GetNotificationsByUserIdAsync(string tenantId, Guid userId, int? PageNumber, int? PageSize);
    Task<List<FormFieldValueDto>> GetFormFieldValues(string? tenantId, Guid? entityId);
    Task<List<string>> GetCustomFieldsNames(string tenantId, Guid? entityId);
    Task<List<GoogleAdsNamesAuthResponseDto>> GetGoogleAdsAccountsAsync(LeadSource leadSource, string tenantId);
    Task<DateTime?> GetLatestLeadHistoryModificationTimeAsync(Guid leadId, string tenantId);
    Task<int?> GetLatestLeadHistoryVersionAsync(Guid leadId, string tenantId);
}
