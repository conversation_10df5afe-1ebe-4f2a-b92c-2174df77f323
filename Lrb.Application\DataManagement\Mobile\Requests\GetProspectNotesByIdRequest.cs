﻿using Lrb.Application.Common.Gmail;
using Lrb.Application.Common.Interfaces;
using Lrb.Application.Common.Persistence;
using Lrb.Application.DataManagement.Mobile.Dtos;
using Lrb.Application.DataManagement.Mobile.Specs;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.DataManagement.Mobile.Requests
{
    public class GetProspectNotesByIdRequest : IRequest<Response<Dictionary<DateTime, List<ProspectHistoryDto>>>>
    {
        public Guid Id { get; set; }
        public bool? CanAccessAllProspects { get; set; }
        public GetProspectNotesByIdRequest(Guid id, bool? canAccessAllProspects)
        {
            Id = id;
            CanAccessAllProspects = canAccessAllProspects;
        }
    }

    public class GetProspectNotesByIdRequestHandler : IRequestHandler<GetProspectNotesByIdRequest, Response<Dictionary<DateTime, List<ProspectHistoryDto>>>>
    {
        private readonly IRepositoryWithEvents<Prospect> _prospectRepo;
        private readonly IRepositoryWithEvents<ProspectHistory> _prospectHistoryRepo;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        public GetProspectNotesByIdRequestHandler(
            IRepositoryWithEvents<Prospect> prospectRepo, 
            IRepositoryWithEvents<ProspectHistory> prospectHistoryRepo,
            IDapperRepository dapperRepository,
            ICurrentUser currentUser)
        {
            _prospectRepo = prospectRepo;
            _prospectHistoryRepo = prospectHistoryRepo;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }

        public async Task<Response<Dictionary<DateTime, List<ProspectHistoryDto>>>> Handle(GetProspectNotesByIdRequest request, CancellationToken cancellationToken)
        {
            var existingProspect = (await _prospectRepo.GetByIdAsync(request.Id, cancellationToken));
            if(existingProspect == null) 
            {
                throw new NotFoundException("Data Not Found By this Id");
            }
            List<ProspectHistory> histories = null;
            var currentUserId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            List<Guid> subIds = new();
            var isAdmin = await _dapperRepository.IsAdminAsync((currentUserId), tenantId ?? string.Empty);
            if (isAdmin)
            {
                histories = await _prospectHistoryRepo.ListAsync(new GetProspectHistoryByProspectIdSpecs(request.Id));
            }
            else
            {
                subIds = (await _dapperRepository.GetSubordinateIdsAsync(currentUserId, tenantId ?? string.Empty, request?.CanAccessAllProspects, isAdmin))?.ToList() ?? new();
                subIds.AddRange(new List<Guid>() { currentUserId, existingProspect.AssignTo });
                subIds = subIds?.Where(i => i != Guid.Empty)?.Distinct()?.ToList() ?? new List<Guid>();
                histories = await _prospectHistoryRepo.ListAsync(new GetProspectHistoryByProspectIdSpecs(request.Id, subIds));
                if (histories == null || histories.Count == 0)
                { histories = await _prospectHistoryRepo.ListAsync(new GetProspectHistoryByProspectIdSpecs(request.Id)); }
            }
            if (!histories?.Any() ?? true)
            {
                return null;
            }
            List<ProspectHistory>? dataHistories = new();
            foreach (var history in histories)
            {
                if(history.FieldName == "Notes")
                {
                    if (history.NewValue != null  || history.OldValue != null)
                    {
                        dataHistories.Add(history);
                    }
                } 
            }
            var datas = dataHistories.OrderByDescending(i => i.ModifiedOn);
            dataHistories.OrderByDescending(i => i.ModifiedOn);
            Dictionary<DateTime, List<ProspectHistory>> prospectHistories = new();
            dataHistories.GroupBy(i => i.GroupKey).ToList();
            dataHistories.ForEach(i => i.ModifiedOn = i?.ModifiedOn.Value.ToLocalTime("India Standard Time"));
            var groups = dataHistories.GroupBy(i => i.ModifiedOn).ToList();
            foreach (var group in groups)
            {
                prospectHistories.Add(group.Key ?? DateTime.Now, group.ToList());
            }
            var dto = prospectHistories.Adapt<Dictionary<DateTime, List<ProspectHistoryDto>>>();
            return new(dto.OrderByDescending(i => i.Key).ToDictionary(i => i.Key, j => j.Value.ToList()));
        }
    }
}
