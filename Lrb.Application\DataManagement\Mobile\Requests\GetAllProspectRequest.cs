﻿using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.DataManagement.Mobile.Dtos;
using Lrb.Application.DataManagement.Mobile.Specs;
using System.ComponentModel;

namespace Lrb.Application.DataManagement.Mobile.Requests
{
    public class GetAllProspectFilterParameter : PaginationFilter
    {
        public string? ProspectSearch { get; set; }
        public ProspectVisiblity ProspectVisiblity { get; set; }
        public ProspectFilterType? FilterType { get; set; }
        public List<EnquiryType>? EnquiryTypes { get; set; }
        public ProspectDateType? DateType { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public List<Guid>? AssignTo { get; set; }
        public List<Guid>? Source { get; set; }
        public List<Budget>? Budget { get; set; }
        public List<string>? Projects { get; set; }
        public List<string>? Properties { get; set; }
        public List<double>? NoOfBHKs { get; set; }
        public List<BHKType>? BHKTypes { get; set; }
        public List<Guid>? PropertyType { get; set; }
        public List<Guid>? PropertySubType { get; set; }
        public long? MinBudget { get; set; }
        public long? MaxBudget { get; set; }
        public List<Guid>? LastModifiedByIds { get; set; }
        public List<Guid>? AssignedFromIds { get; set; }
        public List<string>? SubSources { get; set; }
        public List<string>? AgencyNames { get; set; }
        public List<Guid>? StatusIds { get; set; }
        public List<Guid>? CreatedByIds { get; set; }
        public List<string>? CompanyNames { get; set; }
        public List<Guid>? ClosingManagers { get; set; }
        public List<Guid>? SourcingManagers { get; set; }
        public List<Profession>? Profession { get; set; }
        public List<Guid>? ProspectIds { get; set; }
        public List<string>? Locations { get; set; }
        public double? CarpetArea { get; set; }
        public double? MinCarpetArea { get; set; }
        public double? MaxCarpetArea { get; set; }
        public Guid? CarpetAreaUnitId { get; set; }
        public bool? IsWithTeam { get; set; }
        public List<string>? Designations { get; set; }
        public List<Guid>? DeletedByIds { get; set; }
        public List<string>? Cities { get; set; }
        public List<Guid>? RestoredByIds { get; set; }
        public List<Guid>? QualifiedByIds { get; set; }
        public List<Guid>? ConvertedByIds { get; set; }
        public string? Currency { get; set; }
        public List<Guid>? CustomFilterIds { get; set; }
        public double? BuiltUpArea { get; set; }
        public double? MinBuiltUpArea { get; set; }
        public double? MaxBuiltUpArea { get; set; }
        public Guid? BuiltUpAreaUnitId { get; set; }
        public double? SaleableArea { get; set; }
        public double? MinSaleableArea { get; set; }
        public double? MaxSaleableArea { get; set; }
        public Guid? SaleableAreaUnitId { get; set; }
        public string? ReferralName { get; set; }
        public string? ReferralContactNo { get; set; }
        public string? ReferralEmail { get; set; }
        public List<int>? Beds { get; set; }
        public List<int>? Baths { get; set; }
        public List<string>? Floors { get; set; }
        public List<OfferType>? OfferTypes { get; set; }
        public List<FurnishStatus>? Furnished { get; set; }
        public List<string>? Communities { get; set; }
        public List<string>? SubCommunities { get; set; }
        public List<string>? TowerNames { get; set; }
        public List<string>? Localities { get; set; }
        public List<string>? Countries { get; set; }
        public double? NetArea { get; set; }
        public double? MinNetArea { get; set; }
        public double? MaxNetArea { get; set; }
        public Guid? NetAreaUnitId { get; set; }
        public double? PropertyArea { get; set; }
        public double? MinPropertyArea { get; set; }
        public double? MaxPropertyArea { get; set; }
        public Guid? PropertyAreaUnitId { get; set; }
        public string? UnitName { get; set; }
        public List<string>? ClusterName { get; set; }
        public List<string>? Nationality { get; set; }
        public List<string>? UnitNames { get; set; }
        public List<string>? ChannelPartnerNames { get; set; }
        public List<string>? CampaignNames { get; set; }
        public List<Purpose>? Purposes { get; set; }
        public long? FromMinBudget { get; set; }
        public long? ToMinBudget { get; set; }
        public long? FromMaxBudget { get; set; }
        public long? ToMaxBudget { get; set; }
        public string? UploadTypeName { get; set; }
        public bool? CanAccessAllProspects { get; set; }  
        public DateTime? ToPossesionDate { get; set; }
        public DateTime? FromPossesionDate { get; set; }
        public PossesionType? PossesionType { get; set; }
        public List<string>? LandLine { get; set; }
        public List<string>? PropertyToSearch { get; set; }
        public List<string>? CountryCode { get; set; }
        public List<string>? AltCountryCode { get; set; }
        public List<Gender>? GenderTypes { get; set; }
        public List<MaritalStatusType>? MaritalStatuses { get; set; }
        public DateTime? DateOfBirth { get; set; }
    }
    public class GetAllProspectRequest : GetAllProspectFilterParameter, IRequest<PagedResponse<ViewProspectDto, long>>
    {
        
    }

    public class GetAllProspectRequestHandler : IRequestHandler<GetAllProspectRequest, PagedResponse<ViewProspectDto, long>>
    {
        private readonly IRepositoryWithEvents<Prospect> _prospectRepo;
        private readonly ICurrentUser _currentUser;
        private readonly IProspectRepository _efProspectRepository;
        private readonly IDapperRepository _dapperRepository;
        private readonly IRepositoryWithEvents<CustomProspectStatus> _customProspectRepo;

        public GetAllProspectRequestHandler(
            IRepositoryWithEvents<Prospect> prospectRepo,
            ICurrentUser currentUser,
            IProspectRepository efProspectRepository,
            IDapperRepository dapperRepository,
            IRepositoryWithEvents<CustomProspectStatus> customProspectRepo)
        {
            _prospectRepo = prospectRepo;
            _currentUser = currentUser;
            _efProspectRepository = efProspectRepository;
            _dapperRepository = dapperRepository;
            _customProspectRepo = customProspectRepo;
        }
        public async Task<PagedResponse<ViewProspectDto, long>> Handle(GetAllProspectRequest request, CancellationToken cancellationToken)
        {
            var userId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            List<Guid> subIds = new();

            try
            {
                if (request?.AssignTo?.Any() ?? false)
                {
                    if (request?.IsWithTeam ?? false)
                    {
                        subIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.AssignTo, tenantId ?? string.Empty)).ToList();
                    }
                    else
                    {
                        subIds = request?.AssignTo ?? new List<Guid>();
                    }
                }
                else
                {
                    subIds = (await _dapperRepository.GetSubordinateIdsAsync(userId, tenantId ?? string.Empty, request?.CanAccessAllProspects))?.ToList() ?? new();
                }
            }
            catch
            {

            }
            List<CustomProspectStatus> statuses =null;
            try
            {
                if (!string.IsNullOrWhiteSpace(request.ProspectSearch) && request.PropertyToSearch != null && request.PropertyToSearch.Contains("Status"))
                {
                    statuses = await _customProspectRepo.ListAsync(new Web.Request.GetProspectStatusByNameSpecs(request.ProspectSearch));
                }
                else
                {
                    statuses = await _customProspectRepo.ListAsync(new GetProspectStatusForMobileSpecs());
                }
            }
            catch (Exception ex)
            {
            }
            var prospects = _efProspectRepository.GetAllProspectForMobile(request, userId, subIds, statuses).Result.ToList();
            var count = _efProspectRepository.GetAllProspectCountForMobile(request, userId, subIds, statuses).Result;
            var prospectsView = prospects.Adapt<List<ViewProspectDto>>();
            return new(prospectsView, count);
        }
    }

    public enum ProspectFilterType
    {
        All,
        Followups,
        Backlog,
        New,
        Qualified,
        NotReachable,
        NotInterested,
        NotAnswered,
        InValid,
        Converted
    }

    public enum ProspectDateType
    {
        All = 0,
        CreatedDate,
        ModifiedDate,
        ScheduleDate,
        QualifiedDate,
        ConvertedDate,
        PossessionDate
    }

    public enum ProspectVisiblity
    {
        [Description("SelfWithReportee")]
        SelfWithReportee = 0,
        [Description("Self")]
        Self,
        [Description("Reportee")]
        Reportee,
        [Description("ConvertedData")]
        ConvertedData,
        [Description("DeletedLeads")]
        DeletedData,
        [Description("UnassignLead")]
        UnassignData,
    }
}
