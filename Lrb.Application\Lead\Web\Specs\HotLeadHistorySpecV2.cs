﻿using System.Linq;

namespace Lrb.Application.Lead.Web.Specs
{
    public class HotLeadHistorySpecV2 : Specification<LeadHistoryHot>
    {
        public HotLeadHistorySpecV2(Guid leadId)
        {
            Query.Where(i => i.LeadId == leadId).OrderBy(i => i.ModifiedOn);
        }
        public HotLeadHistorySpecV2(Guid leadId, Guid userId)
        {
            Query.Where(i => i.LeadId == leadId && i.UserId == userId);
        }
        public HotLeadHistorySpecV2(Guid leadId, Guid userId, Guid secondryUserId)
        {
            Query.Where(i => i.LeadId == leadId && (i.UserId == userId || i.UserId == secondryUserId));
        }
        public HotLeadHistorySpecV2(Guid leadId, List<Guid> userIds)
        {
            Query.Where(i => i.LeadId == leadId && userIds.Contains(i.UserId ?? Guid.Empty));
        }
        public HotLeadHistorySpecV2(List<Guid> leadIds)
        {
            Query.Where(i => !i.IsDeleted && leadIds.Contains(i.LeadId));
        }
    }
}
