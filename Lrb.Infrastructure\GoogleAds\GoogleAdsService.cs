﻿using Amazon.S3;
using Finbuckle.MultiTenant;
using Google.Ads.GoogleAds;
using Google.Ads.GoogleAds.Config;
using Google.Ads.GoogleAds.Lib;
using Google.Ads.GoogleAds.V18.Errors;
using Google.Ads.GoogleAds.V20.Services;
using Lrb.Application.Common.Facebook;
using Lrb.Application.Common.GoogleAd;
using Lrb.Application.Common.GoogleAds;
using Lrb.Application.Common.Persistence;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Infrastructure.GoogleAds;
using Lrb.Infrastructure.Persistence;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Npgsql;
using Serilog;
using static Google.Ads.GoogleAds.V18.Errors.AuthenticationErrorEnum.Types;

public class GoogleAdsService : IGoogleAdsService
{
    private readonly GoogleAdsSettings _googleadsettings;
    private AmazonS3Client _s3Client;
    private readonly GoogleAdsClient _googleAdsClient;
    private readonly DatabaseSettings _dbSettings;
    private readonly ITenantInfo? _currentTenant;
    private readonly ILogger _logger;
    private readonly ILeadRepositoryAsync _leadRepositoryAsync;
    private readonly IDapperRepository _dapperRepository;
    public string? ClientId { get; set; }
    public string? ClientSecret { get; set; }
    public string? DeveloperToken { get; set; }
    public string? LoginCustomerId { get; set; }
    public GoogleAdsService(IOptions<DatabaseSettings> dbSettings,
        ITenantInfo currentTenant,
        Serilog.ILogger logger,
        IOptions<GoogleAdsSettings> options,
        ILeadRepositoryAsync leadRepositoryAsync,
        IDapperRepository dapperRepository)
    {
        _dbSettings = dbSettings.Value;
        _currentTenant = currentTenant;
        _googleAdsClient = new GoogleAdsClient();
        _logger = logger;
        _leadRepositoryAsync = leadRepositoryAsync;
        _dapperRepository = dapperRepository;
        _googleadsettings = options.Value;
        ClientId = _googleadsettings.ClientId;
        ClientSecret = _googleadsettings.ClientSecret;
        DeveloperToken = _googleadsettings.DeveloperToken;
    }
    public async Task<bool> IsGoogleAdsAccountExists(string googleUserId, string? connectionString = null)
    {
        if (string.IsNullOrEmpty(connectionString))
        {
            connectionString = string.IsNullOrEmpty(_currentTenant?.ConnectionString) ? _dbSettings.ConnectionString : _currentTenant.ConnectionString;
        }
        NpgsqlConnection conn = new(connectionString);
        var query = $"SELECT * FROM \"LeadratBlack\".\"GoogleAdsAuthResponses\" WHERE \"CustomerId\" = '{googleUserId}' AND \"IsDeleted\" = 'false';";
        await conn.OpenAsync();
        bool result = false;
        using (var command = new NpgsqlCommand(query, conn))
        {
            command.CommandType = System.Data.CommandType.Text;
            var reader = await command.ExecuteReaderAsync();
            result = reader.HasRows ? true : false;
        }
        await conn.CloseAsync();
        return result;
    }
    public async Task<string> GetTenantIdByGoogleAdsAccountId(string googleUserId, string connectionString = null)
    {
        if (string.IsNullOrEmpty(connectionString))
        {
            connectionString = string.IsNullOrEmpty(_currentTenant?.ConnectionString) ? _dbSettings.ConnectionString : _currentTenant.ConnectionString;
        }
        NpgsqlConnection conn = new(connectionString);
        var query = $"SELECT * FROM \"LeadratBlack\".\"GoogleAdsAuthResponses\" WHERE \"CustomerId\" = '{googleUserId}' AND \"IsDeleted\" = 'false';";
        await conn.OpenAsync();
        string tenantId = string.Empty;
        using (var command = new NpgsqlCommand(query, conn))
        {
            command.CommandType = System.Data.CommandType.Text;
            var reader = await command.ExecuteReaderAsync();
            if (reader.HasRows)
            {
                while (await reader.ReadAsync())
                {
                    tenantId = reader["TenantId"].ToString() ?? string.Empty;
                }
            }
        }
        await conn.CloseAsync();
        return tenantId;
    }

    public async Task<GoogleAdsAccountResponse?> GetCustomerDetailsAsync(string customerId, string token)
    {
        try
        {

            var config = new GoogleAdsConfig()
            {
                OAuth2ClientId = _googleadsettings.ClientId,
                OAuth2ClientSecret = _googleadsettings.ClientSecret,
                OAuth2RefreshToken = token,
                DeveloperToken = _googleadsettings.DeveloperToken,
                LoginCustomerId = customerId
            };
            var client = new GoogleAdsClient(config);
            var googleAdsService = client.GetService(Services.V20.GoogleAdsService);
            string query = @"
                    SELECT 
                        customer.resource_name,
                        customer.id, 
                        customer.descriptive_name
                    FROM customer
                    LIMIT 1";

            var request = new SearchGoogleAdsRequest
            {
                CustomerId = customerId,
                Query = query
            };

            var response = googleAdsService.SearchAsync(request);

            await foreach (var row in response)
            {
                _logger.Information($" customer found with ID: {row.Customer.ResourceName + row.Customer.Id.ToString() + row.Customer.DescriptiveName}");
                // Map the Google Ads response to your DTO
                return new GoogleAdsAccountResponse
                {
                    ResourceName = row.Customer.ResourceName,
                    Id = row.Customer.Id.ToString(),
                    DescriptiveName = row.Customer.DescriptiveName
                };
            }
            // No results found
            _logger.Information($"No customer found with ID: {customerId}");
            return null;
        }
        catch (GoogleAdsException ex)
        {
            // Handle Google Ads specific errors
            _logger.Error($"Google Ads API error for customer {customerId}: {ex.Message}");

            // Handle specific error cases
            foreach (var error in ex.Failure.Errors)
            {
                if (error.ErrorCode.AuthenticationError == AuthenticationError.CustomerNotFound)
                {
                    _logger.Information($"Customer {customerId} not found");
                    return null;
                }
            }
            return null;
        }
        catch (Exception ex)
        {
            _logger.Error($"Unexpected error getting customer details for {customerId}: {ex.Message}");
            throw;
        }
    }
    public async Task<GoogleAdsAdAccountsResponse?> GetAllGoogleAdAccountsAsync(string? customerId, string refreshToken)
    {
        try
        {
            GoogleAdsAdAccountsResponse googleAdAccountsResponse = new()
            {
                data = new List<GoogleAdsAdAccountInfo>(),
                paging = new GoogleAdAccountsPaging()
            };

            var config = new GoogleAdsConfig()
            {
                DeveloperToken = _googleadsettings.DeveloperToken,
                OAuth2ClientId = _googleadsettings.ClientId,
                OAuth2ClientSecret = _googleadsettings.ClientSecret,
                OAuth2RefreshToken = refreshToken
            };

            var client = new GoogleAdsClient(config);

            var gaService = client.GetService(Services.V20.GoogleAdsService);

            var query = @"
                    SELECT
                      customer_client.client_customer,
                      customer_client.level,
                      customer_client.hidden,
                      customer_client.time_zone,
                      customer_client.id,
                      customer_client.descriptive_name,
                      customer_client.currency_code,
                      customer_client.manager,
                      customer_client.status
                    FROM customer_client
                    WHERE customer_client.level = 1";
            var request = new SearchGoogleAdsRequest
            {
                CustomerId = customerId,
                Query = query
            };

            await foreach (var row in gaService.SearchAsync(request))
            {
                var clientCustomer = row.CustomerClient;

                googleAdAccountsResponse.data.Add(new GoogleAdsAdAccountInfo
                {
                    Id = clientCustomer.Id.ToString(),
                    Name = clientCustomer.DescriptiveName
                });
            }
            return googleAdAccountsResponse;
        }
        catch (Exception ex)
        {
            var error = new LrbError()
            {
                ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                ErrorSource = ex?.Source,
                StackTrace = ex?.StackTrace,
                InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings
                {
                    ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                    Formatting = Formatting.Indented
                }),
                ErrorModule = "GoogleAdsService -> GetAllGoogleAdAccountsAsync()"
            };
            await _leadRepositoryAsync.AddErrorAsync(error);
            throw;
        }
    }
    public async Task<GoogleAdResponse> GetAllGoogleAdsAsync(string customerId, string refreshToken)
    {
        try
        {
            GoogleAdResponse googleAdResponse = new()
            {
                data = new List<GoogleAdsAdDto>(),
                paging = new GoogleAdAccountsPaging()
            };

            var config = new GoogleAdsConfig()
            {
                DeveloperToken = _googleadsettings.DeveloperToken,
                OAuth2ClientId = _googleadsettings.ClientId,
                OAuth2ClientSecret = _googleadsettings.ClientSecret,
                OAuth2RefreshToken = refreshToken,
                LoginCustomerId = customerId
            };

            GoogleAdsClient client = new(config);
            var gaService = client.GetService(Services.V20.GoogleAdsService);
            var customerService = client.GetService(Services.V20.CustomerService);
            var accessibleCustomers = customerService.ListAccessibleCustomers();
            string customerQuery = @"
                SELECT
                  customer.id,
                  customer.descriptive_name,
                  customer.currency_code,
                  customer.time_zone
                FROM customer";
            var customerRequest = new SearchGoogleAdsRequest()
            {
                CustomerId = customerId,
                Query = customerQuery
            };
            var CustomerResponse = gaService.SearchAsync(customerRequest);
            string currencyCode = string.Empty;
            string countryCode = string.Empty;
            await foreach (var row in CustomerResponse)
            {
                var customer = row.Customer;
                currencyCode = customer.CurrencyCode;
                var timeZone = customer.TimeZone;
                countryCode = GetCountryCodeFromCurrency(currencyCode);
            }
            var query = @"
                    SELECT
                      ad_group_ad.ad.id,
                      ad_group_ad.ad.name,
                      ad_group_ad.status,
                      ad_group.id,
                      ad_group.name,
                      campaign.id,
                      campaign.name
                    FROM ad_group_ad";
            var request = new SearchGoogleAdsRequest()
            {
                CustomerId = customerId,
                Query = query
            };

            var response = gaService.SearchAsync(request);


            await foreach (var row in response)
            {
                var ad = row.AdGroupAd.Ad;
                var adGroup = row.AdGroup;
                var campaign = row.Campaign;

                googleAdResponse.data.Add(new GoogleAdsAdDto
                {
                    id = ad.Id.ToString(),
                    name = ad.Name,
                    effective_status = row.AdGroupAd.Status.ToString(),
                    adset_id = adGroup.Id.ToString(),
                    adset_name = adGroup.Name,
                    campaign_id = campaign.Id.ToString(),
                    campaign_name = campaign.Name,
                    ad_account_id = customerId,
                    CurrencyCode = currencyCode,
                    countryCode = countryCode,
                    CustomerId = customerId,

                });
            }

            return googleAdResponse;
        }
        catch (Exception ex)
        {
            var error = new LrbError()
            {
                ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                ErrorSource = ex?.Source,
                StackTrace = ex?.StackTrace,
                InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                ErrorModule = "GoogleAdsService -> GetAllGoogleAdsAsync()"
            };
            await _leadRepositoryAsync.AddErrorAsync(error);
            throw;
        }
    }
    public async Task<string> GetCustomerNameAsync(string customerId, GoogleAdsClient client)
    {
        var googleAdsService = client.GetService(Services.V20.GoogleAdsService);

        var query = "SELECT customer.descriptive_name FROM customer";

        var request = new SearchGoogleAdsRequest
        {
            CustomerId = customerId,
            Query = query,
        };

        try
        {
            var response = googleAdsService.SearchAsync(request);

            var result = await response.ReadPageAsync(1); // Only one customer expected

            return result.FirstOrDefault()?.Customer?.DescriptiveName ?? string.Empty;
        }
        catch (GoogleAdsException ex)
        {
            Console.WriteLine($"Google Ads API error: {ex.Message}");
            return string.Empty;
        }
    }

    public async Task<List<GoogleAdOrFormWithUserOrPageTokenDto>> GetAdsAndFormsWithUserOrAccessTokenAsync(string? tenantId)
    {
        var query = $"select distinct(flg.\"AdId\") as \"GoogleAdsId\", flg.\"Id\", 'Ad' as \"Type\", flg.\"Status\", flg.\"IsSubscribed\", flg.\"AutomationId\", agency.\"Name\", ias.\"AssignedUserIds\", ias.\"ProjectIds\", flg.\"TenantId\", far.\"Id\" as \"FacebookAuthResponseId\", far.\"LongLivedUserAccessToken\" as \"FacebookUserOrPageAccessToken\" from \"LeadratBlack\".\"FacebookAdsInfo\" flg\r\nleft join \"LeadratBlack\".\"FacebookAuthResponses\" far\r\non far.\"Id\" = flg.\"FacebookAuthResponseId\"\r\nleft join \"LeadratBlack\".\"IntegrationAssignmentInfo\" ias\r\non ias.\"Id\" = flg.\"AutomationId\"\r\nleft join \"LeadratBlack\".\"Agencies\" agency\r\non flg.\"AgencyId\" = agency.\"Id\"\r\nwhere flg.\"Status\" ilike '%active%'\r\nand flg.\"IsDeleted\" = 'false'\r\nand flg.\"IsSubscribed\" = 'true'\r\nand flg.\"TenantId\" = '{tenantId}' and flg.\"PageId\" in (select \"FacebookId\" from \"LeadratBlack\".\"FacebookConnectedPageAccount\" where \"TenantId\" = '{tenantId}' and \"IsDeleted\" = 'false')" +
            $"\r\nunion" +
            $"\r\nselect distinct(flg.\"FacebookId\") as \"FacebookId\", flg.\"Id\", 'Form' as \"Type\", flg.\"Status\", flg.\"IsSubscribed\", flg.\"AutomationId\", agency.\"Name\", ias.\"AssignedUserIds\", ias.\"ProjectIds\", flg.\"TenantId\", far.\"Id\" as \"FacebookAuthResponseId\", fcp.\"LongLivedPageAccessToken\" as \"FacebookUserOrPageAccessToken\" from \"LeadratBlack\".\"FacebookLeadGenForm\" flg\r\nleft join \"LeadratBlack\".\"FacebookConnectedPageAccount\" fcp\r\non flg.\"PageId\" = fcp.\"FacebookId\"\r\nleft join \"LeadratBlack\".\"IntegrationAssignmentInfo\" ias\r\non ias.\"Id\" = flg.\"AutomationId\"\r\nleft join \"LeadratBlack\".\"Agencies\" agency\r\non flg.\"AgencyId\" = agency.\"Id\"\r\nleft join \"LeadratBlack\".\"FacebookAuthResponses\" far\r\non far.\"Id\" = (select \"FacebookAuthResponseId\" from \"LeadratBlack\".\"FacebookConnectedPageAccount\" where \"Id\" = flg.\"FacebookConnectedPageAccountId\" limit 1)\r\nwhere flg.\"Status\" ilike '%active%'\r\nand flg.\"IsDeleted\" = 'false'\r\nand flg.\"IsSubscribed\" = 'true'\r\nand flg.\"TenantId\" = '{tenantId}' and flg.\"PageId\" in (select \"FacebookId\" from \"LeadratBlack\".\"FacebookConnectedPageAccount\" where \"TenantId\" = '{tenantId}' and" +
            $" \"IsDeleted\" = 'false')" +
            $"ORDER BY \"Type\" ASC;";
        var result = await _dapperRepository.QueryAsync<GoogleAdOrFormWithUserOrPageTokenDto>(query);
        return result?.DistinctBy(i => i.GoogleAdsId)?.ToList() ?? new();
    }
    public async Task<GoogleAdsBulkLeadDto> GetBulkLeadInfoAsync(string formOrAdOrAdGroupId, string userOrPageAccessToken, DateTime? fromDateTime = null, DateTime? toDateTime = null)
    {
        var filters = new List<FacebookFilter>();
        GoogleAdsBulkLeadDto leadDto = new();
        if (fromDateTime != null)
        {
            var fromDateUnixTimeStamp = fromDateTime <= DateTime.MinValue.AddHours(10)
                ? new DateTimeOffset(DateTime.MinValue.AddHours(10), TimeSpan.Zero).ToUnixTimeSeconds().ToString()
                : new DateTimeOffset(fromDateTime.Value).ToUnixTimeSeconds().ToString();
            filters.Add(new("time_created", FacebookFilter.GREATER_THAN, fromDateUnixTimeStamp));
        }
        if (toDateTime != null)
        {
            var toDateUnixTimeStamp = new DateTimeOffset(toDateTime.Value).ToUnixTimeSeconds().ToString();
            filters.Add(new("time_created", FacebookFilter.LESS_THAN, toDateUnixTimeStamp));
        }
        string? after = null;
        return leadDto ?? new();
    }
    public async Task<GoogleAdsUserAccessTokenResponse?> GetLongLivedUserAccessTokenAsync(string token)
    {
        var clientId = _googleadsettings.ClientId;
        var clientSecret = _googleadsettings.ClientSecret;
        var refreshToken = token;
        using var httpClient = new HttpClient();
        var request = new HttpRequestMessage(HttpMethod.Post, "https://oauth2.googleapis.com/token")
        {
            Content = new FormUrlEncodedContent(new Dictionary<string, string>
        {
            { "client_id", clientId },
            { "client_secret", clientSecret },
            { "refresh_token", refreshToken },
            { "grant_type", "refresh_token" }
        })
        };

        var response = await httpClient.SendAsync(request);
        if (!response.IsSuccessStatusCode)
            return null;

        var json = await response.Content.ReadAsStringAsync();
        return JsonConvert.DeserializeObject<GoogleAdsUserAccessTokenResponse>(json);
    }
    private string GetCountryCodeFromCurrency(string currencyCode)
    {
        return currencyCode switch
        {
            "INR" => "IN",
            "USD" => "US",
            "EUR" => "EU",
            "GBP" => "GB",
            _ => "UNKNOWN"
        };
    }
}